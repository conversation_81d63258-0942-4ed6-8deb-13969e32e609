 
@model YseStoreAdmin.Pages.Components.Products.ProductEditToolBar 
@{
}
<div>
	<table border="0" cellpadding="5" cellspacing="0" class="r_con_table">
		<thead>
			<tr>
				<td width="1%" nowrap="nowrap">序号</td>
				<td width="10%" nowrap="nowrap">用户名</td>
				<td width="20%" nowrap="nowrap">功能模块</td>
				<td width="" nowrap="nowrap">操作内容</td>
				<td width="20%" nowrap="nowrap">IP地址</td>
				<td width="130" nowrap="nowrap">操作时间</td>
				<td width="37" nowrap="nowrap" class="operation"></td>
			</tr>
		</thead>
		<tbody>

			@if (Model.LogList != null && Model.LogList.Count > 0)
			{
				foreach (var (item,idx) in Model.LogList.WithIndex())
				{
					<tr>
						<td nowrap="nowrap">@(Model.LogList.PageIndex*Model.LogList.PageSize+idx+1)</td>
						<td nowrap="nowrap">@item.UserName</td>
						<td nowrap="nowrap">
							@item.ModuleName
						</td>
						<td>
							@item.Log															:<div class="desc">@item.LogDesc		</div>
						</td>
						<td nowrap="nowrap">
							@item.IP								<div class="desc">日本</div>
						</td>
						<td nowrap="nowrap">@item.AccDate</td>
						<td nowrap="" class="operation side_by_side">
							<a class="icon_file oper_icon button_tips btn_view_detail" href="javascript:;" data-lid="4325">详细</a>
						</td>
					</tr>
				}

			}

		</tbody>
	</table>
</div>