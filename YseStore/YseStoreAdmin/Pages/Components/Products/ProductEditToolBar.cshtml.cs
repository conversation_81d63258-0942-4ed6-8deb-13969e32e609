using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService.manage;
using YseStore.Model.Event;
using YseStore.Model.VM;

namespace YseStoreAdmin.Pages.Components.Products
{
    public class ProductEditToolBar : MComponent
    {

        public IManageOperationLogService LogSer { get; }


        public PagedList<VM_OperationLog>? LogList { get; set; }

        public ProductEditToolBar(IManageOperationLogService logSer)
        {

            Subscribe<UserTurnPageEvent>(HandleUserTurnPage);
            LogSer = logSer;
        }
        public async Task HandleUserTurnPage(UserTurnPageEvent evt)
        {
            if (evt.Name == "manlog")
            {
                await BindData(evt.PageNum);
            }
        }

        public override async Task MountAsync()
        {
            await BindData();
        }
        [SkipOutput]
        public async Task BindData(int page = 1)
        {
            LogList = await GetLogs(page);
            DispatchGlobal<PageEvent>(new PageEvent(LogList.TotalCount, LogList.PageSize, LogList.PageIndex + 1, "manlog"), null, true);

        }

        public async Task<PagedList<VM_OperationLog>> GetLogs(int page = 1)
        {
            var result = await LogSer.QueryAsync(page, 50);

            return result;
        }

    }
}
