@using Newtonsoft.Json.Linq
@model YseStoreAdmin.Pages.Components.Store.VisualComp.PluginsBlocks
@{
    var pluginsConfig = Model.obj["pluginsConfig"].ToObject<JObject>();
    var pluginsRow = Model.obj["pluginsRow"].ToObject<JObject>();
    string BlocksName = Model.obj["BlocksName"].ToString();
    var BlocksValue = Model.obj["BlocksValue"].ToObject<JObject>();


    string key = BlocksName;
    if (pluginsConfig["Blocks"][key] != null)
    {
        string blocksType = BlocksName.Split('-')[0];
        if (pluginsConfig["Blocks"][blocksType] != null)
        {
            key = blocksType;
        }
        else
        {
            key = blocksType + "-1";
        }
    }


}

<div>

    <div class="fixed_global" data-plugins="@(pluginsRow["PId"])-@BlocksName">
        <div class="goback">返回<i></i></div>
        <div class="tool_bar_bg">
            <div class="plugins_settings">
                @{
                    var blockKey = pluginsConfig["Blocks"][key];
                    if (blockKey != null)
                    {

                        @foreach (var item in blockKey.ToObject<JObject>().Properties())
                        {

                            // string InputName = "";
                            string InputName = $"visual[PId-{pluginsRow["PId"].ToString()}][Blocks][{BlocksName}][{item.Name}]";
                            var config = pluginsConfig["Blocks"][key][item.Name];
                            string Value = "";
                            if (BlocksValue[item.Name] != null)
                            {
                                Value = BlocksValue[item.Name].ToString();
                            }


                            //内容 plugins-item
                            <plugins-item params="new { Name = item.Name, Value = Value, Config = config, InputName = InputName }"></plugins-item>

                        }
                    }
                }


            </div>
        </div>

        @if (pluginsConfig["Config"]["BlocksAdd"] != null || pluginsConfig["Config"]["BlocksAdd"].ObjToBool() == true)
        {
            <div class="tool_bar_bg">
                <div class="plugins_delete">删除</div>
            </div>
        }

    </div>

</div>