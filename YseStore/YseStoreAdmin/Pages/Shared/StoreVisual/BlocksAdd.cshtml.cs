using Aliyun.OSS;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using YseStore.Common;
using YseStore.Common.Helper;
using YseStore.IService;
using YseStore.IService.Visual;

namespace YseStoreAdmin.Pages.Shared.StoreVisual
{
    public class BlocksAddModel : PageModel
    {

        private readonly IViewRenderService _viewRenderService;
        private readonly IConfigService _configService;
        private readonly IVisualDraftsService _visualDraftsService;
        public readonly IVisualPagesService _visualPagesService;
        public readonly IVisualPluginsService _visualPluginsService;

        public BlocksAddModel(IViewRenderService viewRenderService, IConfigService configService, IVisualDraftsService visualDraftsService, IVisualPagesService visualPagesService, IVisualPluginsService visualPluginsService)
        {
            _viewRenderService = viewRenderService;
            _configService = configService;
            _visualDraftsService = visualDraftsService;
            _visualPagesService = visualPagesService;
            _visualPluginsService = visualPluginsService;
        }

        public void OnGet()
        {
        }

        /// <summary>
        /// 添加模块
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> OnPostJsonOutput()
        {
            try
            {
                int PId = Request.GetFormInt("PId", 0);
                int DraftsId = Request.GetFormInt("DraftsId", 0);
                string Blocks = Request.GetFormString("Blocks", string.Empty);

                var pluginsRow = await _visualPluginsService.QueryById(PId);
                if (pluginsRow == null)
                {
                    return new JsonResult(new
                    {
                        msg = new { Error = "插件不存在" },
                        ret = 0
                    });
                }

                var draft = await _visualDraftsService.QueryById(DraftsId);
                if (draft == null)
                {
                    return new JsonResult(new
                    {
                        msg = new { Error = "草稿不存在" },
                        ret = 0
                    });
                }

                string type = pluginsRow.Type;
                string mode = pluginsRow.Mode;

                JObject blocks = pluginsRow.Blocks.JsonToObj<JObject>();


                var config = await _visualPluginsService.GetPluginsConfig(type, mode, draft);
                if (config == null)
                {
                    return new JsonResult(new
                    {
                        msg = new { Error = "插件配置不存在" },
                        ret = 0
                    });
                }

                // 添加内容默认为复制第一个
                var firstProperty = config["Blocks"].ToObject<JObject>().First as JProperty;

                string key = firstProperty?.Name;

                if (!Blocks.IsNullOrEmpty())
                {
                    if (config["Blocks"][Blocks] != null)
                    {
                        key = Blocks;
                    }
                    else
                    {
                        key = Blocks + "-1";
                    }
                }



                return new JsonResult(new
                {
                    msg = new { Success = "添加成功" },
                    ret = 1,
                    data = new
                    {
                        Blocks = blocks[key],
                        key = key,
                        PId = PId,
                        DraftsId = DraftsId,
                        type = type,
                        mode = mode
                    }
                });

            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    msg = new { Error = ex.Message },
                    ret = 0
                });
            }

        }
    }
}
