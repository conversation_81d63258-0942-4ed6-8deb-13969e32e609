using Entitys;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using YseStore.IService.Visual;
using YseStore.IService;
using YseStoreAdmin.Controllers;
using Newtonsoft.Json.Linq;
using YseStore.Common;
using YseStore.Common.Helper;

namespace YseStoreAdmin.Pages.Products
{
    public class ProductEditToolbarModel : PageModel
    {
        public readonly IVisualDraftsService _visualDraftsService;
        public readonly IVisualPagesService _visualPagesService;
        public readonly IVisualPluginsService _visualPluginsService;
        public readonly IVisualTemplateServicee _visualTemplateServicee;
        public readonly IConfigService _configService;

        private readonly ILogger<CodeEditController> _logger;
        public ProductEditToolbarModel(IVisualDraftsService visualDraftsService, IVisualPagesService visualPagesService, IVisualPluginsService visualPluginsService, IVisualTemplateServicee visualTemplateServicee, ILogger<CodeEditController> logger, IConfigService configService)
        {
            _visualDraftsService = visualDraftsService;
            _visualPagesService = visualPagesService;
            _visualPluginsService = visualPluginsService;
            _visualTemplateServicee = visualTemplateServicee;
            _logger = logger;
            _configService = configService;
        }

        public void OnGet()
        {
        }

        public int type { get; set; } = 0;
        public JObject GlobalObj { get; set; } = new JObject();//ȫ�����
        public JObject Blocks { get; set; } = new JObject();//������

        public JObject FixedPlugins { get; set; } = new JObject();//Ĭ�����
        /// <summary>
        /// �����Ҳ���Ŀ������
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> OnPost()
        {
            var page = Request.GetFormString("page");
            var DraftsId = Request.GetFormInt("DraftsId");
            var id = Request.GetFormString("id");
            var PagesId = Request.GetFormInt("PagesId");
            var PId = Request.GetFormString("PId");
            string PIdSecName = PId;



            if (DraftsId == 0)
            {
                var dfId = await _configService.GetConfigValueByGroup("global", "DraftsIdV2");
                if (!dfId.IsNullOrEmpty())
                {
                    DraftsId = dfId.ObjToInt();
                }

            }

            var shopLangList = await _configService.GetConfigValueByGroup("global", "shopLangList");

            //�ݸ�
            var draft = await _visualDraftsService.QueryById(DraftsId);
            if (draft == null)
            {
                return Content("");
            }

            var themesConfig = draft.Config.JsonToObj<JObject>();

            var themesConfigJson = await System.IO.File.ReadAllTextAsync(System.IO.Path.Combine(System.Environment.CurrentDirectory, $"wwwroot/template/default/inc/themes.json"));
            var themesConfigModel = themesConfigJson.JsonToObj<JObject>();
            themesConfigModel = themesConfigModel["Config"].ToObject<JObject>();

            // ȫ������->��Ʒ->��Ʒͼ->��Ʒͼ����, �鹹��Ʒͼ��������, ProductImageʵ�ʿ���ProductsPicScale��ProductsMainPicScale By Ziruo
            JObject producImage = themesConfigModel["ProductsPicScale"].ToObject<JObject>();
            //producImage["expand"] = producImage["expand"] ?? new JObject();
            //producImage["expand"]["hint"] = "tips";

            themesConfigModel["ProductImage"] = producImage;
            themesConfigModel["ProductImage"]["expand"] = themesConfigModel["ProductImage"]["expand"] ?? new JObject();
            themesConfigModel["ProductImage"]["expand"]["hint"] = "tips";
            //ҳ��
            var pageRow = await _visualPagesService.QueryByClauseAsync(it => it.PagesId == PagesId && it.DraftsId == DraftsId && it.Pages == page);
            if (pageRow == null)
            {
                //return Content("");
            }


            // Define the pageAry list
            List<string> pageAry = new List<string>
            {
                "index", "list", "goods", "article", "cases",
                "cases-detail", "news", "news-detail", "blog",
                "blog-detail", "download-list", "download"
            };

            // Define the singlePage list
            List<string> singlePage = new List<string>
            {
                "landing_page", "cod_page"
            };

            // Define the modulePageAry list
            List<string> modulePageAry = new List<string>
            {
                "article", "list", "goods"
            };

            //���
            var pluginsConfig = new JObject();
            var pluginsRow = new visual_plugins();
            if (!page.IsNullOrEmpty() && (pageAry.Contains(page) || singlePage.Contains(page)) && !PIdSecName.Contains("-"))
            {
                pluginsRow = await _visualPluginsService.QueryById(PId);
                if (pluginsRow == null)
                {
                    return Content("");
                }

                string type = pluginsRow.Type;
                string mode = pluginsRow.Mode;

                pluginsConfig = await _visualPluginsService.GetPluginsConfig(type, mode, draft);
                if (pluginsConfig == null)
                {
                    return Content("");
                }

            }

            var PIdAry = PIdSecName.Split('-');
            if (PIdSecName == "global-set") //ȫ���������
            {
                GlobalObj = new JObject();
                GlobalObj["pluginsRow"] = themesConfig;
                GlobalObj["pluginsConfig"] = themesConfigModel;
                GlobalObj["shopLangList"] = shopLangList;

                //return PartialView("tool_bar_fixed_global", data);
                type = 1;
            }
            else if (PIdAry.Count() > 1) //����������
            {

                Blocks["BlocksName"] = $"{PIdAry[0]}-{PIdAry[1]}";
                Blocks["BlocksValue"] = pluginsConfig["Blocks"][PIdSecName];
                Blocks["pluginsConfig"] = pluginsConfig;
                Blocks["pluginsRow"] = JObject.FromObject(pluginsRow);

                type = 2;
                //return PartialView("plugins_blocks", data);
            }
            else //�������
            {
                FixedPlugins["page"] = page;
                FixedPlugins["pluginsRow"] = JObject.FromObject(pluginsRow);
                FixedPlugins["pluginsConfig"] = pluginsConfig;
                FixedPlugins["shopLangList"] = shopLangList;
                FixedPlugins["fiexPagesAry"] = new JArray("list", "article");

                type = 0;
                //return PartialView("tool_bar_fixed_plugins", data);
            }


            return Page();
        }

    }
}
