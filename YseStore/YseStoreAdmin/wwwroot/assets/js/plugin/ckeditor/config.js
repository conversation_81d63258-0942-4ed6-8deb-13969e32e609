/**
 * @license Copyright (c) 2003-2015, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or http://ckeditor.com/license
 */

CKEDITOR.editorConfig = function( config ) {
	config.filebrowserUploadUrl='/manage/action/file-upload-ckeditor';
	config.filebrowserImageUploadUrl='/manage/action/file-upload-ckeditor?file_type=img';
	config.filebrowserFlashUploadUrl='/manage/action/file-upload-ckeditor?file_type=flash';
	config.toolbarCanCollapse=true;
	config.enterMode=CKEDITOR.ENTER_BR;
	config.fontSize_sizes='10px;12px;14px;15px;16px;18px;20px;22px;24px;28px;36px;48px;60px;72px';
	config.width='100%';
	config.height=400;
	config.line_height="120%;150%;180%;200%;250%;300%;350%;400%";
	config.font_names='Arial;<PERSON>l Black/arial black,<PERSON>l;Arial;黑体/黑体,Arial;隶书/隶书,<PERSON>l;宋体/宋体,Arial;新宋体/新宋体,Arial;微软雅黑/微软雅黑,Arial;楷体_GB2312/楷体_GB2312,Arial;Zawgyi-One/Zawgyi-One,Arial;Calibri';

	//基础工具栏
	config.toolbar='simple';
	config.toolbar_simple=[
		{ name: 'document', items: [ 'Source' ] },
		{ name: 'paragraph', items: [ 'CreateDiv', '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock' ] },
		{ name: 'links', items: [ 'Link', 'Unlink' ] },
		{ name: 'insert', items: [ 'ueeshopimage', 'Image' ] },
		{ name: 'styles', items: [ 'Font', 'FontSize', 'lineheight' ] },
		{ name: 'colors', items: [ 'TextColor', 'BGColor' ] },
		{ name: 'basicstyles', items: [ 'Bold', 'Italic', 'Underline', 'Strike'] }
	];

	//可视化工具栏
	config.toolbar='visual';
	config.toolbar_visual = [
		{ name: 'styles', items: ['FontSize'] },
		{ name: 'colors', items: ['TextColor' ] },
		{ name: 'basicstyles', items: [ 'Bold', 'Italic'] },
		{ name: 'links', items: [ 'Link', 'Unlink' ] }
	];
	config.toolbarCanCollapse = false;
	config.removePlugins = 'resize,elementspath';

	//完整工具栏
	config.toolbar='full';
	config.toolbar_full=[
		{ name: 'document', items: [ 'Source', '-', 'NewPage', 'Preview', 'Print', '-', 'Templates' ] },
		{ name: 'clipboard', items: [ 'Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', 'RemoveFormat', '-', 'Undo', 'Redo' ] },
		{ name: 'editing', items: [ 'Find', 'Replace', '-', 'SelectAll', '-', 'Scayt' ] },
		{ name: 'tools', items: [ 'Maximize', 'ShowBlocks' ] },
		'/',
		{ name: 'paragraph', items: ['Outdent', 'Indent', '-', 'Blockquote', 'CreateDiv', '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock', '-', 'BidiLtr', 'BidiRtl', 'Language' ] },
		{ name: 'links', items: [ 'Link', 'Unlink', 'Anchor' ] },
		{ name: 'insert', items: [ 'ueeshopimage', 'Table', 'Youtube', 'HorizontalRule', 'Smiley', 'SpecialChar', 'PageBreak', 'Iframe' ] },
		'/',
		{ name: 'styles', items: ['Format', 'Font', 'FontSize', 'lineheight' ] },
		{ name: 'colors', items: [ 'TextColor', 'BGColor' ] },
		{ name: 'basicstyles', items: [ 'Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript' ] }
	];
	config.extraPlugins='shopimage';
	config.allowedContent=true;
	config.pasteFromWordRemoveFontStyles=false;//不清除word文档格式
	config.pasteFromWordRemoveStyles=false;//不清除word文档格式
};