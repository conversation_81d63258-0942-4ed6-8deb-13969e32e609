
var view_obj = {
	visual_init: function () {
		// 公共初始化
		var global_init = function () {
			// 端口切换(移动|电脑|全屏)
			$('#plugins_visual .top_bar .go_screen i').on('click', function () {
				setProgressRate();
				let screen = $(this).data('type');
				client = $(this).data('client');
				// 着色
				$(this).addClass('current').siblings().removeClass('current');
				$('#plugins_visual .main_bar, #plugins_visual .tool_bar').removeClass('web mweb full').addClass(screen);
				// 切换完端口后初始化内容
				setTimeout(() => {  // 宽度转变时间为0.2s
					let jumpUrl = iframeUrl;
					jumpUrl = getClientUrl(jumpUrl);
					$('#plugins_iframe_themes').attr('src', jumpUrl);
				}, 500);
			});
			// 发布操作
			$('#plugins_visual .top_bar .go_publish').off().on('click', function () {
				// 发布;
				if (!publishStatus) return false;
				publishStatus = false;
				let DraftsId = $('#plugins_visual input[name=DraftsId]').val();  // 草稿箱ID
				$.post('/manage/view/visual-v2/publish/', { DraftsId: DraftsId, IsTmp: 1, returnType: 1 }, function (data) {
					let storeUrl = $('#plugins_visual input[name=storeUrl]').val();  // 单个页面模式地址
					let _jumpStore = '';
					if (storeUrl) {
						_jumpStore = '&store=' + storeUrl;
					}
					if (data.ret == 1) {
						publishStatus = true;
						global_obj.win_alert_auto_close(lang_obj.manage.module.drafts_publish_success, '', 1000, '8%');
						window.location.href = '/manage/view/visual-v2/edit?DraftsId=' + data.msg + '&publicFull=1' + _jumpStore;
					} else {
						global_obj.win_alert_auto_close(lang_obj.global.save_fail, 'fail', 1000, '8%');
						if (data.msg) {
							window.location.href = '/manage/view/visual-v2/edit?DraftsId=' + data.msg + '&publicFull=1' + _jumpStore;
						} else {
							window.location.href = '/manage/view/visual-v2/';
						}
					}
				}, 'json');
			});
			// 浏览器状态切换
			document.addEventListener("visibilitychange", function () {
				if (document.visibilityState == 'visible') {
					// 切换后重新加载头底部,有导航跳转外部设置,等以后可以选择,应该可以把这个关回来
					var newObjAry = new Array
					let permitAry = ['header', 'footer', 'gallery', 'blog', 'news', 'cases'];
					for ($k = 0; $k < permitAry.length; $k++) {
						let objAry = $('#plugins_visual .tool_bar .icon_' + permitAry[$k]);
						if (objAry.length > 0) {
							for ($i = 0; $i < objAry.length; $i++) {
								Id = $(objAry[$i]).parent('.menu_item').data('fixed-plugins');
								if ($(`#plugins_visual .tool_bar .fixed_global[data-plugins='${Id}']`).length) {
									newObjAry.push($(`#plugins_visual .tool_bar .fixed_global[data-plugins='${Id}']`));
								}
							}
						}
					}
					data_init.apply(newObjAry);

					check_plugins_status();

				}
			});

			// 查看店铺
			$('#view_shop').on('click', function () {
				let storeModule = $(this).attr('data-store');
				if (storeModule) {
					window.open(storeModule);
				} else {
					$('#header .menu .menu_home a').trigger('click');
				}
			});
			// 左上角点击切换页面
			$('#plugins_visual .top_bar .go_select').on('click', function () {
				$(this).toggleClass('current');
			});
			$('body').on('click', function (e) {
				if (e.target.className.indexOf('go_select') == -1 && e.target.className.indexOf('page_show_item') == -1) {
					$('#plugins_visual .top_bar .go_select').removeClass('current');
				}
			})

			// 左上角切换页面
			$('#plugins_visual .top_bar .go_select .page_select_box').on('click', '.select_item', function (event) {
				event.stopPropagation();
				let selectBox = $(this).parents('.page_select_box');
				let isSub = $(this).data('sub');
				if (isSub == 1) {  // 弹出下级
					let id = $(this).data('id');
					let subObj = selectBox.find(`.sub_box[data-id=${id}]`);
					if (!subObj.length) return;
					selectBox.find('.menu_box').hide();
					subObj.show();
				} else if ($(this).hasClass('back_item')) {  // 返回按钮
					$(this).parents('.sub_box').hide();
					selectBox.find('.menu_box').show();
				} else {  // 跳转
					let dataUrl = $(this).attr('iframe-url');
					if (!dataUrl) return false;
					let Title = $(this).text();
					setProgressRate();
					iframeUrl = dataUrl;
					let jumpUrl = iframeUrl;
					jumpUrl = getClientUrl(jumpUrl);
					$('#plugins_visual .tool_bar .loading_mask').fadeIn();
					$('#plugins_visual .main_bar .loading_mask').fadeIn();
					setTimeout(() => {
						$('#plugins_iframe_themes').attr('src', jumpUrl);
					}, 500);
					$('#plugins_visual .top_bar .go_select .show_page_item').html(Title);
					$(this).parents('.go_select').removeClass('current');
					if ($(this).parents('.sub_box').length) $(this).parents('.sub_box').find('.back_item').trigger('click');
					// 重置搜索框
					let searchObj = $(this).parents('.menu_item').siblings('.search_item');
					if (searchObj.length) searchObj.find('.form_input').val('').siblings('.search_btn').trigger('click');
					// edit_tool_bar初始化
					_init = false;
				}
			});
			// 左上角切换页面, 产品搜索
			$('#plugins_visual .top_bar .go_select .page_select_box .search_item input').on('click', function (event) {
				event.stopPropagation();
				if ($(this).hasClass('form_input')) return false;
				let value = $(this).prev('.form_input').val();
				let subBox = $(this).parents('.sub_box');
				subBox.find('.menu_item').each(function () {
					let text = $(this).find('.select_item').text();
					text.indexOf(value) == -1 ? $(this).hide() : $(this).show();
				});
				value ? subBox.find('.pages_menu_btn').hide() : subBox.find('.pages_menu_btn').show();
			});
			// 左上角切换页面, 添加产品详细
			$('#plugins_visual .top_bar .go_select .page_select_box').on('click', '#add_product', function (event) {
				event.stopPropagation();
				let params = { iframeTitle: lang_obj.manage.view.select_products, type: 'manual', isOrder: true, value: {}, valueOrder: [] };
				frame_obj.products_choice_iframe_init_v2({
					params: params,
					onSelect: (data) => {
						onSelectFunc(data, 'add');
					},
					onSubmit: (data) => {
						$(this).addClass('loading');
						let proid = typeof data.value[0] == 'undefined' ? 0 : data.value[0].proid;
						if (!proid) return false;
						let proidAry = [];
						for (let key in data.value) {
							proidAry.push(data.value[key].proid);
						}
						let draftsId = $('#plugins_visual input[name=DraftsId]').val();
						$.post('/manage/view/visual-v2/add-product-descript', { DraftsId: draftsId, id: proid, idAry: proidAry }, (result) => {
							$(this).removeClass('loading');
							if (result.ret == 1) {
								deleteSameId(result.msg.delPagesId);
								$(this).before(result.msg.menuItem);
								global_obj.win_alert_auto_close(lang_obj.global.add_success, '', 2000, '8%');
								$(`#plugins_visual .top_bar .go_select .page_select_box .sub_box .select_item[data-pages-id='${result.msg.pagesId}']`).trigger('click');
								save_visual_form();
							} else {
								global_obj.win_alert_auto_close(result.msg, 'await', 2000, '8%');
							}
						}, 'json');
					}
				});
			});
			//左上角切换页面，添加单页、产品列表页
			$('#plugins_visual .top_bar .go_select .page_select_box').on('click', '#add_article, #add_product_category', function (event) {
				event.stopPropagation();
				let _this = $(this);
				let draftsId = $('#plugins_visual input[name=DraftsId]').val();
				let _id = 0;
				let _idAry = [];
				let _page = $(this).parent().attr('data-id');
				$.post('/manage/view/visual-v2/add-product-descript', { DraftsId: draftsId, id: _id, idAry: _idAry, Pages: _page }, (result) => {
					_this.removeClass('loading');
					if (result.ret == 1) {
						deleteSameId(result.msg.delPagesId, _page);
						_this.before(result.msg.menuItem);
						global_obj.win_alert_auto_close(lang_obj.global.add_success, '', 2000, '8%');
						$(`#plugins_visual .top_bar .go_select .page_select_box .sub_box .select_item[data-pages-id='${result.msg.pagesId}']`).trigger('click');
						save_visual_form();
					} else {
						global_obj.win_alert_auto_close(result.msg, 'await', 2000, '8%');
					}
				}, 'json');
			});
			// 左上角切换页面, 复制产品详细模板
			$('#plugins_visual .top_bar .go_select .page_select_box').on('click', '.copy_item', function (event) {
				event.stopPropagation();
				let draftsId = $('#plugins_visual input[name=DraftsId]').val();
				let pagesId = $(this).parent('.menu_item').find('.select_item').data('pages-id');
				$.post('/manage/view/visual-v2/copy-product-descript', { DraftsId: draftsId, PagesId: pagesId }, (data) => {
					if (data.ret == 1) {
						copyObj = data.msg.copyObj
						$(`#plugins_visual .top_bar .go_select .page_select_box #add_${copyObj}`).before(data.msg.menuItem);
						global_obj.win_alert_auto_close(lang_obj.manage.module.drafts_copy_success, '', 2000, '8%');
						$(`#plugins_visual .top_bar .go_select .page_select_box .sub_box .select_item[data-pages-id='${data.msg.pagesId}']`).trigger('click');
						save_visual_form();
					}
				}, 'json');
			});
			// 左上角切换页面, 删除产品详细模板
			$('#plugins_visual .top_bar .go_select .page_select_box').on('click', '.del_item', function (event) {
				event.stopPropagation();
				let draftsId = $('#plugins_visual input[name=DraftsId]').val();
				let pagesId = $(this).parent('.menu_item').find('.select_item').data('pages-id');
				let currentPagesId = $('#plugins_visual input[name=PagesId]').val();
				let params = {
					'title': lang_obj.global.del_confirm,
					'confirmBtn': lang_obj.global.del,
					'confirmBtnClass': 'btn_warn'
				};
				global_obj.win_alert(params, () => {
					save_visual_form(
						'',
						() => {
							$.post('/manage/view/visual-v2/del-product-descript', { DraftsId: draftsId, PagesId: pagesId }, (data) => {
								if (data.ret == 1) {
									if (pagesId == currentPagesId) $(this).parents('.page_select_box').find('.menu_box .menu_item .select_item.icon_item_index').trigger('click');
									$(this).parents('.menu_item').remove();
									global_obj.win_alert_auto_close(lang_obj.manage.global.del_success, '', 2000, '8%');
								}
							}, 'json');
						});
				}, 'confirm');
			});
			// iframe发生变化
			iframe_change_init();
			// 显示手机版二维码
			$('#view_shop i').on('mouseenter', () => {
				$('#view_shop .qrcode').slideDown('fast');
			}).on('mouseout', () => {
				$('#view_shop .qrcode').slideUp('fast');
			}).on('click', () => {
				return false;
			});


			// 监听iframe内a链接点击
			window.addEventListener('aClick', function (event) {
				let href = event.detail.href;
				if (href != 'javascript:;' || href == '' || href == '#') {
					iframeUrl = href;
					if (!iframeUrl) iframeUrl = '/';
					let jumpUrl = iframeUrl;
					jumpUrl = getClientUrl(jumpUrl);
					$('#plugins_visual .main_bar .loading_mask').fadeIn();
					$('#plugins_visual .tool_bar .loading_mask').fadeIn();
					setTimeout(() => {
						$('#plugins_iframe_themes').attr('src', jumpUrl);
					}, 500);
					_init = false;
					$('#plugins_visual input[name=CurrentPluginsId]').val('');
				}
			});
			window.addEventListener('bodyClick', function (event) {
				if (event.detail.target.indexOf('go_select') == -1 && event.detail.target.indexOf('page_show_item') == -1) {
					$('#plugins_visual .top_bar .go_select').removeClass('current');
				}
			});

			/*** 内容区操作start ***/


			window.addEventListener('showBlockOperate', function (event) {
				let _type = event.detail.type;
				let _pid = event.detail.pid;
				let _showHtml = event.detail.showHtml;
				let _clickEvent = event.detail.clickEvent;
				let _typeClass = '.item_mode';
				if (_type == 'header' || _type == 'footer') _typeClass = '.item_name';
				let _pluginsChinaName = $(`#plugins_visual .tool_bar .tool_bar_menu .menu_item[data-fixed-plugins=${_pid}] ${_typeClass}`).html();
				let pluginsContent = $('#plugins_iframe_themes').contents().find(`*[data-visual-id='${_pid}']`).parent('.visual_plugins_container');

				if (_pluginsChinaName && !pluginsContent.find('.plugins_type_box').length) {
					_showHtml = _showHtml.replace('{typeName}', _pluginsChinaName);
					pluginsContent.append(_showHtml)
					let _contentTop = pluginsContent.offset().top;
					if (_contentTop == 0) {
						pluginsContent.find('.plugins_type_box').css('top', 0)
					}
				}
				
				if (_clickEvent) { //点击内容区域
					if (!$(`#plugins_visual .tool_bar_menu .menu_item[data-fixed-plugins=${_pid}]`).hasClass('menu_item_current')) {
						$(`#plugins_visual .tool_bar_menu .menu_item`).removeClass('block_item_operate')
						$(`#plugins_visual .tool_bar_menu .menu_item[data-fixed-plugins=${_pid}]`).addClass('block_item_operate')
						$(`#plugins_visual .tool_bar .fixed_global.fixing .goback`).trigger('click');
						$(`#plugins_visual .tool_bar_menu .menu_item[data-fixed-plugins=${_pid}]`).trigger('click');
					}
					let _operateInfo = ['operate_up', 'operate_down', 'operate_copy', 'operate_hide', 'operate_del'];
					let _disabledOperateType = ['header', 'footer'];
					var _pluginsSortAry = {};
					$('#plugins_iframe_themes').contents().find('.visual_plugins_container:visible').each(function (index) {
						let _curPluginsId = $(this).find('div[data-visual-id]').attr('data-visual-id');
						_pluginsSortAry[_curPluginsId] = index
					})
					var _curPluginsSort = _pluginsSortAry[_pid];
					var _movePluginsLength = Object.keys(_pluginsSortAry).length - 2;

					let _operateItemHtml = `
						<div class="operate_item_box" data-plugins="${_pid}">
						`;
					for (k in _operateInfo) {
						let _operateName = _operateInfo[k];
						let _operateClass = '';
						if (_operateName == 'operate_up' || _operateName == 'operate_down') _operateClass = 'operate_move';
						if (_curPluginsSort == 1 && _operateName == 'operate_up') continue;
						if (_curPluginsSort == _movePluginsLength && _operateName == 'operate_down') continue; //除了头部 第一个插件不给向上移动 \ 除了底部 最后一个 
						if ($.inArray(_type, ['article', 'product_purchase', 'product_description', 'combination_purchase', 'product_list', 'blog_list', 'blog_detail']) !== -1 && $.inArray(_operateName, ['operate_copy']) !== -1) continue;
						if ($.inArray(_type, ['blog_detail', 'product_purchase', 'combination_purchase', 'product_list', 'blog_list']) !== -1 && $.inArray(_operateName, ['operate_hide']) !== -1) continue;
						if ($.inArray(_type, ['article', 'collections_list', 'product_list', 'product_purchase', 'cases_list', 'cases_detail', 'news_list', 'news_detail', 'blog_list', 'blog_detail', 'certificates_list', 'download_list', 'cases_description', 'product_description', 'download', 'combination_purchase']) !== -1 && _operateName == 'operate_del') continue;
						_operateItemHtml += `<div class="operate_item ${_operateClass} ${_operateName}"></div>`;
					}
					_operateItemHtml += `
						</div>`;

					if ($.inArray(_type, _disabledOperateType) == -1) {
						pluginsContent.append(_operateItemHtml);
					}

					// 内容区点击删除
					pluginsContent.find('.operate_item_box').on('click', '.operate_del', function () {
						$(`#plugins_visual .tool_bar .tool_bar_fixed .fixed_global[data-plugins=${_pid}] .plugins_delete`).trigger('click');
					})
					// 内容区点击隐藏
					pluginsContent.find('.operate_item_box').on('click', '.operate_hide', function () {
						$(`#plugins_visual .tool_bar .tool_bar_fixed .fixed_global[data-plugins=${_pid}] .goback`).trigger('click');
						$(`#plugins_visual .tool_bar_menu .menu_item[data-fixed-plugins=${_pid}] .item_display`).trigger('click');
					})
					// 内容区点击复制
					pluginsContent.find('.operate_item_box').on('click', '.operate_copy', function () {
						$(`#plugins_visual .tool_bar_menu .menu_item[data-fixed-plugins=${_pid}] .item_copy`).trigger('click');
					})
					// 内容区点击移动
					pluginsContent.find('.operate_item_box').on('click', '.operate_move', function () {
						let iframe = $('#plugins_iframe_themes').contents();
						let _direction = 'up';
						if ($(this).hasClass('operate_down')) _direction = 'down';
						if ($(this).hasClass('operate_moveoperate_move_dis')) return;
						let otherItemId = _direction == 'up' ? pluginsContent.prev('.visual_plugins_container').find('div[data-visual-id]').attr('data-visual-id') : pluginsContent.next('.visual_plugins_container').find('div[data-visual-id]').attr('data-visual-id');
						iframe.find(`*[data-visual-id=${_pid}]`).parents('.visual_plugins_container').animate({ opacity: 0 }, 200, function () {
							let html = $(this).clone();
							$(this).remove();
							let _curPluginsMenuItem = $(`#plugins_visual .tool_bar_menu .menu_item[data-fixed-plugins=${_pid}]`);
							let _movePluginsMenuItem = $(`#plugins_visual .tool_bar_menu .menu_item[data-fixed-plugins=${otherItemId}]`);
							let _visualMenuHtml = _curPluginsMenuItem.clone();
							_curPluginsMenuItem.remove();
							_direction == 'up' ? _movePluginsMenuItem.before(_visualMenuHtml) : _movePluginsMenuItem.after(_visualMenuHtml);
							_direction == 'up' ? iframe.find(`*[data-visual-id=${otherItemId}]`).parents('.visual_plugins_container').before(html) : iframe.find(`*[data-visual-id=${otherItemId}]`).parents('.visual_plugins_container').after(html);
							let _top = iframe.find(`*[data-visual-id=${_pid}]`).parents('.visual_plugins_container').offset().top;
							iframe.find(`*[data-visual-id=${_pid}]`).parents('.visual_plugins_container').animate({ opacity: 1 }, 200);
							iframe.find('body, html').animate({ scrollTop: _top });
							save_visual_form(
								'',
								() => {
									let _curMoveOperate = iframe.find(`*[data-visual-id=${_pid}]`).parents('.visual_plugins_container').find('.operate_item_box');
									let _operateItem = '<div class="operate_item operate_move {operateClass}"></div>';
									if (_curPluginsSort == _movePluginsLength && _direction == 'up' && !_curMoveOperate.find('.operate_down').length) {
										_operateItem = _operateItem.replace('{operateClass}', 'operate_down');
										_curMoveOperate.find('.operate_up').after(_operateItem)
									}
									if (_curPluginsSort == 1 && _direction == 'down' && !_curMoveOperate.find('.operate_up').length) {
										_operateItem = _operateItem.replace('{operateClass}', 'operate_up');
										_curMoveOperate.find('.operate_down').before(_operateItem)
									}
									operate_check_icon(_curMoveOperate, _pid)
									setTimeout(() => {
										iframe.find(`*[data-visual-id=${_pid}]`).parents('.visual_plugins_container').click()
									}, 100)
								});
						});
					})
				}
			});
			window.addEventListener('hideBlockPoerate', function (event) {
				let _pid = event.detail.pid;
				let _clickEvent = event.detail.clickEvent;
				let pluginsContent = $('#plugins_iframe_themes').contents().find(`*[data-visual-id='${_pid}']`).parent('.visual_plugins_container');
				if (!pluginsContent.hasClass('cur_plugins')) {
					pluginsContent.find('.plugins_type_box').remove();
				}
				if (_clickEvent) {
					if (pluginsContent.hasClass('cur_plugins')) {
						$('#plugins_iframe_themes').contents().find('.plugins_type_box').remove();
					}
					$('#plugins_iframe_themes').contents().find('.operate_item_box').remove();
				}
			})
			/*** 内容区操作end ***/
		};

		var check_plugins_status = () => {
			//浏览器窗口切换 判断应用是否开启.
			appObj = $("#plugins_visual .tool_bar *[class*='component_'][data-component][app-type]");
			$typeAry = new Array();
			if (appObj && appObj.length > 0) {
				for ($i = 0; $i < appObj.length; $i++) {
					pluginType = $(appObj[$i]).attr('app-type');
					pluginUsed = $(appObj[$i]).attr('app-used');
					if ($.inArray(pluginType, $typeAry) < 0) {
						$typeAry.push(pluginType);
					}
				}
				if ($typeAry.length > 0) {
					$.post('/manage/view/visual-v2/check-plugin-status', { 'TypeAry': $typeAry }, function (result) {
						if (result.ret == 1) {
							Ary = result.msg;
							for (var key in Ary) {
								if (appObj.attr('data-component') == 'panel') {
									$('#plugins_visual .tool_bar .component_panel[app-type=' + key + ']').each(function ($k, $v) {
										$v = $($v);
										if (Ary[key].status == 1) {
											$v.find('.manage_link').show();
											$v.attr('app-used', Ary[key].status).find('.panel_button').text(Ary[key].text).attr('href', Ary[key].link).attr('target', '_blank');
										} else {
											$v.find('.manage_link').hide();
											$v.attr('app-used', Ary[key].status).find('.panel_button').text(Ary[key].text).attr('href', 'javascript:;').removeAttr('target', '_blank');
										}
									})
								} else if (appObj.attr('data-component') == 'nav') {
									$('#plugins_visual .tool_bar .component_nav[app-type=' + key + ']').each(function ($k, $v) {
										$v = $($v);
										$v.attr('app-used', Ary[key].status).find('.nav_btn').text(Ary[key].text);
										if (Ary[key].status == 1) {
											let _keyName = key[0].toUpperCase() + key.substr(1)
											let _curFilerName = $v.find("input[name*=\\[Settings\\]\\[" + _keyName + "\\]]").attr('data-name') ? $v.find("input[name*=\\[Settings\\]\\[" + _keyName + "\\]]").attr('data-name') : lang_obj.manage.app.screening.visual_plugins_tips
											$v.find('.nav_btn').attr('href', 'javascript:;').removeAttr('target')
											$v.find('.nav_name a').removeClass('hide')
											$v.find('.nav_title').html(_curFilerName)
										} else {
											$v.find('.nav_btn').attr('href', Ary[key].link).attr('target', '_blank')
											$v.find('.nav_name a').addClass('hide')
											$v.find('.nav_title').html(lang_obj.manage.app.screening.visual_plugins_tips)
										}
									})
								}
							}
						}
					}, 'json')
				}
			}
		}

		var operate_check_icon = (obj, _pid) => {
			var _newPluginsSortAry = {};
			_curMoveOperate = obj;
			$('#plugins_iframe_themes').contents().find('.visual_plugins_container:visible').each(function (index) {
				let _newCurPluginsId = $(this).find('div[data-visual-id]').attr('data-visual-id');
				_newPluginsSortAry[_newCurPluginsId] = index
			})
			var _newCurPluginsSort = _newPluginsSortAry[_pid];
			var _newMovePluginsLength = Object.keys(_newPluginsSortAry).length - 2;
			if (_newCurPluginsSort == _newMovePluginsLength) _curMoveOperate.find('.operate_down').remove()
			if (_newCurPluginsSort == 1) _curMoveOperate.find('.operate_up').remove()
		}

		// 导航初始化
		var menu_init = () => {
			// 导航模块点击弹窗
			$('#plugins_visual .tool_bar').on('click', '.tool_bar_menu .menu_item', function (event) {
				if (event.target.className.indexOf('item_display') != -1 || event.target.className.indexOf('item_copy') != -1 || event.target.className.indexOf('icon') != -1) return false;
				let pid = $(this).data('fixed-plugins');
				if (event.target.className.indexOf('item_move') == -1) fiexd_global($(this));
				check_blocks_add_btn(pid);
				// 滑动到当前模块
				$('#plugins_visual .tool_bar .tool_bar_menu .menu_item').removeClass('menu_item_current');
				$(this).addClass('menu_item_current');
				let customEvent = new CustomEvent('visualModuleCurrent', { detail: { pid: pid } });
				$('#plugins_iframe_themes')[0].contentWindow.dispatchEvent(customEvent);
				$('#plugins_visual input[name=CurrentPluginsId]').val(pid);
				if (!$(this).hasClass('block_item_operate')) {
					let pluginsClickEvent = new CustomEvent('pluginsClickCurrent', { detail: { pid: pid } });
					$('#plugins_iframe_themes')[0].contentWindow.dispatchEvent(pluginsClickEvent);
				}
			});
			// 导航模块点击是否显示
			$('#plugins_visual .tool_bar').on('click', '.tool_bar_menu .menu_item .item_display', function () {
				let menu = $(this).parents('.menu_item');
				let input = menu.find("input[name*='[Config][Display]']");
				menu.toggleClass('item_close');
				let value = menu.hasClass('item_close') ? 0 : 1;
				input.val(value);
				let plugins = menu.data('fixed-plugins');
				let pluginsObj = $(`#plugins_visual .tool_bar .fixed_global[data-plugins='${plugins}']`);
				let pluginsContent = $('#plugins_iframe_themes').contents().find(`*[data-visual-id='${plugins}']`).parent('.visual_plugins_container');
				console.log("click visual_plugins_container");
				save_visual_form(
					$(this),
					() => {
						value == 1 ? pluginsContent.fadeIn() : pluginsContent.fadeOut();
						setTimeout(() => {
							document.getElementById('plugins_iframe_themes').contentWindow.location.reload();
						}, 500);
						//data_init.apply(pluginsObj);
					});
			});
			// 导航模块点击复制
			$('#plugins_visual .tool_bar').on('click', '.tool_bar_menu .menu_item .item_copy', function () {
				let draftsId = $('#plugins_visual input[name=DraftsId]').val();
				let pluginsId = $(this).parents('.menu_item').data('fixed-plugins');
				let pagesId = $('#plugins_visual input[name=PagesId]').val();

				save_visual_form(
					$(this),
					() => {
						$.post('/manage/view/visual-v2/plugins-copy', { DraftsId: draftsId, PagesId: pagesId, PId: pluginsId }, (data) => {
							if (data.ret == 1) {
								global_obj.win_alert_auto_close(lang_obj.manage.module.drafts_copy_success, '', 2000, '8%');
								$('#plugins_visual .main_bar .loading_mask').fadeIn();
								$('#plugins_visual .tool_bar .loading_mask').fadeIn();
								_init = false;
								setTimeout(() => {
									document.getElementById('plugins_iframe_themes').contentWindow.location.reload();
								}, 500);
							}
						}, 'json');
					});
			});
			// 全局设置点击弹窗
			$('#plugins_visual .tool_bar').on('click', '#global_set', function () {
				fiexd_global($(this));
			});
			// 全局设置内弹窗点击弹窗
			$('#plugins_visual .tool_bar').on('click', '.global_set_menu .set_item', function () {
				fiexd_global($(this));
			});
			// 产品详细, 添加关联产品
			$('#plugins_visual .tool_bar').on('click', '.related_products .products_btn', function () {
				let draftsId = $('#plugins_visual input[name=DraftsId]').val();
				let pagesId = $('#plugins_visual input[name=PagesId]').val();
				let _OBJParents = $(this).parents('.related_products');
				let _topId = _OBJParents.attr('data-id');
				if (_topId == 'goods') {
					// 产品弹窗
					relatedProducts(draftsId, pagesId);
				} else if (_topId == 'article' || _topId == 'list') { //单页、产品列表页
					relatedFiexdPages(draftsId, pagesId, '', _topId);
				}
				if (!_topId) return false;
			});
			// 产品详细, 关联产品修改
			$('#plugins_visual .tool_bar').on('click', '.related_products .button a.edit', function () {
				let draftsId = $('#plugins_visual input[name=DraftsId]').val();
				let pagesId = $('#plugins_visual input[name=PagesId]').val();
				let proInfo = {};
				let _OBJParents = $(this).parents('.related_products');
				let _topId = _OBJParents.attr('data-id');

				if (_topId == 'goods') {
					$(this).parents('.box').find('.proinfo').each(function (index) {
						let proid = $(this).data('id');
						let img = $(this).data('img');
						proInfo[index] = { proid: proid, image: img };
					});
					// 产品弹窗
					relatedProducts(draftsId, pagesId, proInfo);
				} else if (_topId == 'article' || _topId == 'list') {
					let inputValue = '';
					$(this).parents('.box').find('.proinfo').each(function (index) {
						let _id = $(this).data('id');
						inputValue += _id + ',';
					})
					if (inputValue) inputValue = inputValue.slice(0, -1);
					relatedFiexdPages(draftsId, pagesId, inputValue, _topId);
				}
				if (!_topId) return false;
			});
			// 产品详细, 关联产品删除
			$('#plugins_visual .tool_bar').on('click', '.related_products .button a.del', function () {
				let _OBJParents = $(this).parents('.related_products');
				let _topId = _OBJParents.attr('data-id');
				let params = {
					'title': lang_obj.manage.view.del_related_products[_topId],
					'confirmBtn': lang_obj.global.del,
					'confirmBtnClass': 'btn_warn'
				};
				let draftsId = $('#plugins_visual input[name=DraftsId]').val();
				let pagesId = $('#plugins_visual input[name=PagesId]').val();
				global_obj.win_alert(params, () => {
					$.post('/manage/view/visual-v2/del-product-related', { DraftsId: draftsId, PagesId: pagesId }, (data) => {
						if (data.ret == 1) {
							$(`#plugins_visual .top_bar .go_select .page_select_box .sub_box .select_item[data-pages-id='${pagesId}']`).parent('.menu_item').replaceWith(data.msg.menuItem);
							$(`#plugins_visual .top_bar .go_select .page_select_box .sub_box .select_item[data-pages-id='${pagesId}']`).trigger('click');
						}
					}, 'json');
				}, 'confirm');
			});
			//关联模块 可收缩
			$('#plugins_visual .tool_bar').on('click', '.related_products .down_icon', function () {
				let _OBJ = $(this).parents('.related_products');
				let _thisBox = _OBJ.find('.box');
				$(this).toggleClass('up_toggle');
				_thisBox.toggleClass('box_toggle');
				if (_thisBox.hasClass('box_toggle')) {
					_thisBox.slideUp()
				} else {
					_thisBox.slideDown()
				}
			})
		}
		// 插件初始化
		var plugins_init = () => {
			// 弹窗关闭
			$('#plugins_visual .tool_bar').on('click', '.fixed_global .goback', function () {
				$(this).parents('.fixed_global').removeClass('fixing');
				let _thisparentPlugins = $(this).parents('.fixed_global').data('plugins');
				if ($.isNumeric(_thisparentPlugins)) {
					$(`#plugins_visual .tool_bar_menu .menu_item[data-fixed-plugins=${_thisparentPlugins}]`).removeClass('menu_item_current block_item_operate')
					let pluginsBlackEvent = new CustomEvent('pluginsBlackEvent', { detail: { pid: _thisparentPlugins } });
					$('#plugins_iframe_themes')[0].contentWindow.dispatchEvent(pluginsBlackEvent);
				}
			});
			// Blocks模块弹窗
			$('#plugins_visual .tool_bar').on('click', '.plugins_blocks .menu_item', function () {
				fiexd_global($(this));
			});
			// Blocks添加内容
			$('#plugins_visual .tool_bar').on('click', '.plugins_blocks .menu_btn', function () {
				let pid = $(this).data('pid');
				let draftsId = $('#plugins_visual input[name=DraftsId]').val();
				let blocksAddFunc = (pid, data) => {
					// 插入导航
					$(this).siblings('.blocks_item').append(data.msg.menuHtml);
					// 插入弹窗
					$(this).parents('.tool_bar_fixed').append(data.msg.blocksHtml);
					// 校对添加按钮
					check_blocks_add_btn(pid);
					// 重载函数
					reload_init();
					// 释放按钮
					$(this).removeClass('disabled');
					// 进入内容设置
					setTimeout(() => {
						$(this).siblings('.blocks_item').find('.menu_item:last').trigger('click');
					}, 500);
					// 保存入库
					save_visual_form(
						$(this),
						() => data_init.apply(this)
					);
				}
				if ($(this).data('fixed-plugins') == undefined) {  // 普通类型
					if ($(this).hasClass('disabled')) return false;
					$(this).addClass('disabled');
					$.post('/manage/view/visual-v2/blocks-add', { PId: pid, DraftsId: draftsId }, (data) => {
						if (data.ret == 1) blocksAddFunc.apply(this, [pid, data]);
					}, 'json');
				} else {  // 弹窗类型
					footer_content_init($(this), pid, blocksAddFunc);
					fiexd_global($(this));
				}
			});
			// Blocks删除内容
			$('#plugins_visual .tool_bar').on('click', '.fixed_global .blocks_delete', function () {
				let blocksName = $(this).parents('.fixed_global').data('plugins');
				if (!blocksName) return false;
				let params = {
					'title': lang_obj.global.del_confirm,
					'confirmBtn': lang_obj.global.del,
					'confirmBtnClass': 'btn_warn'
				};
				global_obj.win_alert(params, () => {
					// 删除Blocks导航卡
					let pid = blocksName.split('-')[0];
					$(`#plugins_visual .fixed_global[data-plugins=${pid}] .plugins_blocks .menu_item[data-fixed-plugins=${blocksName}]`).remove();
					// 校对添加按钮
					check_blocks_add_btn(pid);
					// 关闭弹窗
					$(this).parents('.fixed_global').removeClass('fixing');
					setTimeout(() => {
						$(this).parents('.fixed_global').remove();
						save_visual_form(
							$(this),
							() => data_init.apply(this)
						);
					}, 500);
				}, 'confirm');
			});
			// Blocks点击复制
			$('#plugins_visual .tool_bar').on('click', '.plugins_blocks .menu_item .item_copy', function (e) {
				e.stopPropagation()
				let plugins_data = $(this).parents('.menu_item').data('fixed-plugins');
				let draftsId = $('#plugins_visual input[name=DraftsId]').val();
				let pid = $(this).parents('.menu_item').siblings('.menu_btn').data('pid')
				let blocksAddFunc = (pid, data) => {
					// 插入导航
					$(this).parents('.blocks_item').append(data.msg.menuHtml);
					// 插入弹窗
					$(this).parents('.tool_bar_fixed').append(data.msg.blocksHtml);
					// 校对添加按钮
					check_blocks_add_btn(pid);
					// 重载函数
					reload_init();
					// 释放按钮
					$(this).removeClass('disabled');
					// 保存入库
					save_visual_form(
						$(this),
						() => data_init.apply(this)
					);
				}
				if ($(this).data('fixed-plugins') == undefined) {  // 普通类型
					if ($(this).hasClass('disabled')) return false;
					$(this).addClass('disabled');
					$.post('/manage/view/visual-v2/blocks-copy', { Data: plugins_data, DraftsId: draftsId }, (data) => {
						if (data.ret == 1) blocksAddFunc.apply(this, [pid, data]);
					}, 'json');
				} else {  // 弹窗类型
					footer_content_init($(this), pid, blocksAddFunc);
					fiexd_global($(this));
				}
			});
		}
		// 组件初始化
		var component_init = () => {
			/** 字体组件 start */
			// 弹窗
			$('#plugins_visual').on('click', '.component_font .font_btn', function () {
				let inputName = $(this).siblings('input').attr('name');
				let fontName = $(this).siblings('.font_name').text();
				font_select_init(fontName, inputName);
				fiexd_global($(this));
			});
			// 字体变化更新
			$('#plugins_visual').on('change', '.component_font input[type=hidden]', function () {
				let value = $(this).val();
				$(this).siblings('.font_name').css('font-family', value).text(value);
				save_visual_form(
					$(this),
					() => data_init.apply(this)
				);
			});
			/** 字体组件 end */

			/** 效果组件 start */
			// 弹窗
			$('#plugins_visual').on('click', '.component_effect .effect_btn', function () {
				let inputName = $(this).siblings('input').attr('name');
				let effectName = $(this).siblings('input').val();
				let options = $(this).data('options');
				effect_select_init(effectName, inputName, options);
				fiexd_global($(this));
			});
			// 效果变化更新
			$('#plugins_visual').on('change', '.component_effect input[type=hidden]', function () {
				save_visual_form(
					$(this),
					() => data_init.apply(this)
				);
			});
			/** 效果组件 end */

			/** 开关组件 start */
			// 开关初始化
			frame_obj.switchery_checkbox(function (_this) {
				_this.parents('.switch_box').siblings('input[type=hidden]').val(1).trigger('change');
				save_visual_form(
					$(_this),
					() => data_init.apply(_this)
				);
			}, function (_this) {
				_this.parents('.switch_box').siblings('input[type=hidden]').val(0).trigger('change');
				save_visual_form(
					$(_this),
					() => data_init.apply(_this)
				);
			}, '.component_switch .switchery');
			// 开关联动
			$('#plugins_visual').on('change', '.component_switch[data-linkage=true] input[type=hidden]', function () {
				let component = $(this).parents('*[data-component]');
				component_switch_linkage_init.apply(component);
			});
			// 开关弹窗
			$('#plugins_visual').on('click', '.component_switch *[data-fixed-plugins]', function () {
				fiexd_global($(this));
			});
			/** 开关组件 end */

			/** 面板按钮组件 start */
			// 点击开启按钮
			$('#plugins_visual').on('click', '.component_panel .panel_button[data-type=open]', function () {
				$(this).parent('.panel_btn').addClass('open').siblings('input[type=hidden]').val(1).trigger('change');
				save_visual_form(
					$(this),
					() => data_init.apply(this)
				);
			});
			// 点击删除按钮
			$('#plugins_visual').on('click', '.component_panel .panel_button[data-type=delete]', function () {
				$(this).parent('.panel_btn').removeClass('open').siblings('input[type=hidden]').val(0).trigger('change');
				save_visual_form(
					$(this),
					() => data_init.apply(this)
				);
			});
			// 点击编辑按钮把变量替换
			$('#plugins_visual').on('click', '.component_panel .panel_button[data-type=edit]', function () {
				let url = $(this).attr('href');
				if (url.indexOf('{AssociationId}') != -1) {
					let id = $('#plugins_visual input[name=AssociationId]').val();
					url = url.replace('{AssociationId}', id);
					$(this).attr('href', url);
				}
			});
			// 面板按钮联动
			$('#plugins_visual').on('change', '.component_panel[data-linkage=true] input[type=hidden]', function () {
				let component = $(this).parents('*[data-component]');
				component_panel_linkage_init.apply(component);
			});
			/** 面板按钮组件 end */

			/** 分类组件 start */
			// 分类添加弹窗
			$('#plugins_visual').on('click', '.component_category .category_btn', function () {
				let inputName = $(this).siblings('input').attr('name');
				let inputValue = $(this).siblings('input').val();
				category_select_init(inputName, inputValue);
				fiexd_global($(this));
			});
			// 分类修改弹窗
			$('#plugins_visual').on('click', '.component_category .category_box_btn[data-type=edit]', function () {
				let inputName = $(this).parents('.category_box').siblings('input').attr('name');
				let inputValue = $(this).parents('.category_box').siblings('input').val();
				category_select_init(inputName, inputValue);
				fiexd_global($(this));
			});
			// 分类删除
			$('#plugins_visual').on('click', '.component_category .category_box_btn[data-type=delete]', function () {
				$(this).parents('.category_box').hide().siblings('.category_btn').show().siblings('input').val('').trigger('change');
			});
			// 分类变化更新
			$('#plugins_visual').on('change', '.component_category input[type=hidden]', function () {
				let name = $(this).attr('category');
				let style = $(this).attr('style');
				let value = $(this).val();
				if (value) {  // 有选择分类
					$(this).siblings('.category_box').find('.category_image').attr('style', style);
					$(this).siblings('.category_box').find('.category_title').text(name);
					$(this).siblings('.category_box').show().siblings('.category_btn').hide();
				} else {
					$(this).siblings('.category_box').hide().siblings('.category_btn').show();
				}
				autofillInit.call(this, () => {
					save_visual_form(
						$(this),
						() => data_init.apply(this)
					);
				});
			});
			/** 分类组件 end */

			/** 上传图片组件 start */
			// 图片上传
			$('#plugins_visual').on('click', '.component_image .multi_img .upload_btn, .component_font .pic_btn .edit', function () {
				let id = $(this).parents('.multi_img').attr('id');
				frame_obj.photo_choice_init(id, 'visual', 1, '', 1);
			});
			// 图片被改变
			$('#plugins_visual').on('change', '.component_image .multi_img input[type=hidden]', function () {
				pluginsId = $(this).parents('.fixed_global').attr('data-plugins');
				value = $(this).val();
				Name = $(this).attr('name');
				var photo_data = {};
				photo_data.pluginsId = pluginsId;
				photo_data.value = value;
				photo_data.Name = Name;
				if (pluginsId && value) {
					calculateRatio(photo_data, $(this));
				} else {
					save_visual_form(
						$(this),
						() => data_init.apply(this)
					);
				}
			});
			/** 上传图片组件 end */

			/** 文件上传组件start **/
			// 本地视频上传
			$('#plugins_visual').on('click', '.component_video .multi_file .upload_btn', function () {
				let id = $(this).parents('.multi_file').attr('id');
				frame_obj.file_choice_init(id, 'visual', 1, 'mp4', '', 1);
			});

			//  视频被改变
			$('#plugins_visual').on('change', '.component_video .multi_file input[type=hidden]', function () {
				save_visual_form(
					$(this),
					() => data_init.apply(this)
				);
			});

			/** 文件上传组件end **/

			/** 进度条组件 start */
			$('#plugins_visual').on('mousedown', '.component_progress .progress_bar', function (event) {
				let _this = $(this);
				let objLeft = _this.offset().left; // 组件的左位移
				progressScoll(_this, parseFloat(event.clientX) - parseFloat(objLeft));
				$(document).off('mousemove').on('mousemove', function (ev) {
					progressScoll(_this, parseFloat(ev.clientX) - parseFloat(objLeft));
				});
				$(document).on('mouseup', function () {
					$(this).off('mousemove mouseup');
					save_visual_form(
						_this,
						() => data_init.apply(_this)
					);
				});
			});

			/** 进度条组件 end */

			/** 文字组件 start */
			// 文字组件联动弹窗
			$('#plugins_visual').on('click', '.component_word *[data-fixed-plugins]', function () {
				fiexd_global($(this));
			});
			/** 文字组件 end */

			/** 文本对齐方式组件 start */
			$('#plugins_visual').on('click', '.component_textalign .textalign_box .textalign_item', function () {
				let value = $(this).data('value');
				$(this).addClass('current').siblings().removeClass('current');
				$(this).parent('.textalign_box').siblings('input[type=hidden]').val(value);
				save_visual_form(
					$(this),
					() => data_init.apply(this)
				);
			});
			/** 文本对齐方式组件 end */

			/** 下拉框组件 start */
			// 下拉框联动
			$('#plugins_visual').on('change', '.component_select[data-linkage=true] .select_box .box_select input[type=hidden]', function () {
				let component = $(this).parents('*[data-component]');
				component_select_linkage_init.apply(component);
			});
			// 拉下被改变
			$('#plugins_visual').on('change', '.component_select .select_box .box_select input[type=hidden]', function () {
				if ($(this).attr('name').indexOf('[ProductImage]') != -1) {  // 全局设置->产品->产品图->产品图比例, 勾选自动控制原产品列表的比例与详细页的比例 By Ziruo
					let fixed = $(this).parents('.fixed_global');
					let productsPicScale = fixed.find("input[name='visual[Config][ProductsPicScale]']");
					let productsMainPicScale = fixed.find("input[name='visual[Config][ProductsMainPicScale]']");
					productsPicScale.val($(this).val());
					productsMainPicScale.val($(this).val());
				}
				save_visual_form(
					$(this),
					() => data_init.apply(this)
				);
			});
			/** 下拉框组件 end */

			/** 颜色选择组件 start */
			// 颜色被改变
			$('#plugins_visual').on('change', '.component_color input[type=hidden]', function () {
				save_visual_form(
					$(this),
					() => data_init.apply(this)
				);
			});
			/** 颜色选择组件 end */

			/** 单行文本组件组件 start */
			// 修改了文本框
			let componentInputTimer = null;
			$('#plugins_visual').on('input porpertychange', '.component_input input[type=text]', function () {
				if (componentInputTimer) clearTimeout(componentInputTimer);
				componentInputTimer = setTimeout(() => {
					save_visual_form(
						$(this),
						() => data_init.apply(this)
					);
				}, 1500);
			});
			// 单行文本组件联动弹窗
			$('#plugins_visual').on('click', '.component_input *[data-fixed-plugins]', function () {
				fiexd_global($(this));
			});
			/** 单行文本组件组件 end */

			/** 多行文本组件 start */
			// 修改了文本框
			let componentTextareaTimer = null;
			$('#plugins_visual').on('input porpertychange', '.component_textarea textarea', function () {
				if (componentTextareaTimer) clearTimeout(componentTextareaTimer);
				componentTextareaTimer = setTimeout(() => {
					save_visual_form(
						$(this),
						() => data_init.apply(this)
					);
				}, 1500);
			});
			/** 多行文本组件 end */

			/** 隐藏组件 start */
			// 隐藏值发生改变
			$('#plugins_visual').on('change', '.component_hidden input', function () {
				save_visual_form(
					$(this),
					() => data_init.apply(this)
				);
			});
			/** 隐藏组件 end */

			/** 产品组件 start */
			// 添加产品
			$('#plugins_visual').on('click', '.component_products .products_btn', function () {
				// 配置
				let params = {};
				let box = $(this).prev('.products_box');  // 产品容器
				let limit = parseInt(box.data('limit'));  // 限制的产品数量
				let type = box.find('.products_box_type input').val();  // 类型
				let input = box.data('input');
				if (type == 'manual') {
					let value = {};
					let order = [];
					box.find('.products_box_value input').each(function () {
						let proid = $(this).val();
						let image = $(this).data('image');
						value[proid] = { image: image };
						order.push(proid);
					});
					params.value = value;
					params.valueOrder = order;
				} else if (type == 'auto') {
					params.cateid = box.find(`.products_box_value input[name='${input}[cateid]']`).val();
					params.tagid = box.find(`.products_box_value input[name='${input}[tagid]']`).val();
					params.moreid = box.find(`.products_box_value input[name='${input}[more]']`).val();
					params.number = box.find(`.products_box_value input[name='${input}[number]']`).val();
				}
				params.limit = limit;
				params.type = type;
				params.addMethod = limit > 0 ? false : true;
				params.isOrder = true,
					// 产品弹窗
					frame_obj.products_choice_iframe_init_v2({
						params: params,
						onSubmit: function (data) {
							// 类型
							box.find('.products_box_type input').val(data.type);
							// 赋值
							let html = '';
							if (data.type == 'manual') {  // 手动添加
								for (let key in data.value) {
									html += `<input type="hidden" data-image="${data.value[key].image}" name="${input}[value][]" value="${data.value[key].proid}" />`;
								}
							} else if (data.type == 'auto') {  // 智能添加
								for (let key in data.filter) {
									html += `<input type="hidden" name="${input}[${key}]" value="${data.filter[key]}" />`;
								}
							}
							box.find('.products_box_value').html(html);
							box.siblings('.products_name').find('span').text(Object.keys(data.value).length);
							save_visual_form(
								box,
								() => data_init.apply(box)
							);
						}
					});
			});
			/** 产品组件 end */

			/** 链接组件 start */
			// 修改了链接框
			let componentLinkTimer = null;
			$('#plugins_visual').on('input porpertychange', '.component_link input[type=text]', function () {
				if (componentLinkTimer) clearTimeout(componentLinkTimer);
				componentLinkTimer = setTimeout(() => {
					save_visual_form(
						$(this),
						() => data_init.apply(this)
					);
				}, 1500);
			});
			/** 链接组件 end */

			/** 时间组件start **/
			//选择时间
			let componentTimeTimer = null;
			$('#plugins_visual').on('change', '.input_time', function () {
				if (componentTimeTimer) clearTimeout(componentTimeTimer);
				let _thisparent = $(this).parents('.component_time'),
					_startTimeVal = _thisparent.find('input[data-time=starttime]').val(),
					_endTimeVal = _thisparent.find('input[data-time=endtime]').val(),
					_startTime = dataToTime(_startTimeVal),
					_endTime = dataToTime(_endTimeVal),
					_Compared = false;
				if (_startTime && _endTime) {
					if (_startTime >= _endTime) {
						global_obj.win_alert_auto_close(lang_obj.manage.module.deadline_tips, 'fail', 1000, '8%');
						_thisparent.find('input[data-time=endtime]').val('')
						_Compared = false;
					} else {
						_Compared = true;
					}
				} else {
					_Compared = true;
				}
				if (_Compared) {
					componentTimeTimer = setTimeout(() => {
						save_visual_form(
							$(this),
							() => data_init.apply(this)
						);
					}, 1500);
				}
			})

			/** 时间组件end **/

			/** 导航组件 start */
			// 导航添加弹窗
			$('#plugins_visual').on('click', '.component_nav .nav_btn', function () {
				let inputName = $(this).siblings('input').attr('name');
				let inputValue = $(this).siblings('input').val();
				let type = $(this).data('nav');
				if ($(this).parents('.component_nav').attr('app-used') == 1 || type == 'footer_nav') {
					let _addTips = '';
					let multi = $(this).parents('.component_nav').data('multi');
					if (type == 'footer_nav') {
						_addTips = '';
						multi = 0;
					} else if (type == 'filter') {
						_addTips = lang_obj.manage.app.screening.add_tips;
					}
					nav_select_init(inputName, inputValue, type, _addTips, multi);
					fiexd_global($(this));
				}
			});
			// 导航变化更新
			$('#plugins_visual').on('change', '.component_nav input[type=hidden]', function () {
				let name = $(this).attr('nav');
				$(this).siblings('.nav_title').text(name);
				save_visual_form(
					$(this),
					() => data_init.apply(this)
				);
			});
			/** 导航组件 end */

			/** 新闻组件 start */
			// 新闻添加弹窗
			$('#plugins_visual').on('click', '.component_news .news_btn:not(.shop_language_disable)', function () {
				let params = {};
				let box = $(this).prev('.news_box');  // 新闻容器
				let limit = parseInt(box.data('limit'));  // 限制的新闻数量
				let type = box.find('.news_box_type input').val();  // 类型
				let module = box.data('module');
				if (type == 'manual') {
					let value = {};
					let order = [];
					box.find('.news_box_value input').each(function () {
						let newsId = parseInt($(this).val());
						value[newsId] = {};
						order.push(newsId);
					});
					params.value = value;
					params.valueOrder = order;
				}
				params.limit = limit;
				params.type = type;
				params.module = module;
				news_select_init(box, params);
				fiexd_global($(this));
			});
			/** 新闻组件 end */

			$('#plugins_visual').on('input', '.component_link input[type=text]', function () {
				let value = $(this).val(),
					hostname = window.location.hostname,
					length = hostname.length,
					index = value.indexOf(hostname);
				if (index != -1) {
					value = value.substring(index + length);
					$(this).val(value);
				}
			})
		}
		// 添加模块初始化
		var switch_mode_init = () => {
			let waterfallCol = 2;  // 显示个数
			let waterfallColHeight = [];
			let waterfallIndex = 0;  // 当前处理到的个数
			// 瀑布流
			let waterfall = (type) => {
				// 递归获取图片高度
				let getImageSize = (callback) => {
					let imageCallback = () => {
						callback.call(item);
						if (waterfallIndex < itemCount) getImageSize(callback);
						else usable = true;
					}
					let container = $('#mode_list .mode_box .mode_box_container');
					let itemCount = container.find('.mode_item').length;
					let item = container.find('.mode_item').eq(waterfallIndex);
					let image = new Image();
					image.src = item.find('.item_image img').attr('src');
					image.onload = () => {
						imageCallback();
					}
					image.onerror = () => {
						imageCallback();
					}
				}
				// 获取当前item属于那个列容器
				let getitemCol = (type) => {
					if (!waterfallColHeight.length) {
						for (let i = 0; i < waterfallCol; i++) {
							waterfallColHeight[i] = 0;
						}
					}
					let value = type == 'min' ? Math.min.apply(Math, waterfallColHeight) : Math.max.apply(Math, waterfallColHeight);
					value = waterfallColHeight.indexOf(value);
					return value;
				}
				if (type == 'html') {
					waterfallIndex = 0;
					waterfallColHeight = [];
				}
				// 实现瀑布流
				getImageSize(function () {
					let item = $(this);
					let width = 100 / parseInt(waterfallCol);
					item.css({ 'width': `${width}%`, 'visibility': 'hidden' }).show();
					let itemHeight = item.GetTruePixel({ type: 'height' });
					let itemCol = getitemCol(item.hasClass('last_item') ? 'max' : 'min');
					let left = parseFloat(width) * parseInt(itemCol);
					let top = waterfallColHeight[itemCol];
					item.hide().css({ 'position': 'absolute', 'left': `${left}%`, 'top': top, 'visibility': 'visible' }).fadeIn();
					waterfallColHeight[itemCol] += parseFloat(itemHeight);
					waterfallIndex++;
				});
			}
			// 获取分类
			let modeType = '';
			let curPage = '';
			let usable = true;
			let getModeData = (type, pageNumber = 1) => {
				if (!usable) return false;
				let page = $('#plugins_visual input[name=Page]').val();
				let draftsId = $('#plugins_visual input[name=DraftsId]').val();
				let keyword = $.trim($('#mode_list .mode_search .form_input').val());
				let txtName = $(`#mode_list .type_option li[data-type=${type}]`).text();
				let container = $('#mode_list .mode_box .mode_box_container');
				$('#mode_list .type_title').text(txtName);
				$('#mode_list .mode_type').attr('data-type', type);
				usable = false;
				$.post('/manage/view/visual-v2/add-mode-data/', { Type: type, Page: page, DraftsId: draftsId, pageNumber: pageNumber, keyword: keyword }, (data) => {
					if (data.ret == 1) {
						if (pageNumber == 1) $('#mode_list .mode_box').scrollTop(0);
						container.parent('.mode_box').attr({ 'page': pageNumber, 'max-page': data.msg.maxPage });
						pageNumber == 1 ? container.html(data.msg.html) : container.append(data.msg.html);
						pageNumber == 1 ? waterfall('html') : waterfall('append');
						if (pageNumber == 1) {
							if (data.msg.html) {
								$('#mode_list .bg_no_table_data').hide()
								container.show()
							} else {
								$('#mode_list .bg_no_table_data').show()
								container.hide()
							}
						}
					}
				}, 'json');
			}
			// 显示插件分类
			$('#mode_list .mode_type').hover(function () {
				$(this).find('.type_title').addClass('current');
				$(this).find('.type_option').show().stop(true).animate({ 'top': 40, 'opacity': 1 }, 250);
			}, function () {
				$(this).find('.type_title').removeClass('current');
				$(this).find('.type_option').stop(true).animate({ 'top': 30, 'opacity': 0 }, 250, function () { $(this).hide(); });
			});
			// 弹窗开启
			$('#plugins_visual .tool_bar').on('click', '.tool_bar_menu .menu_btn', () => {
				let page = $('#plugins_visual input[name=Page]').val();
				$('#mode_list').fadeIn();
				if (curPage == page && $('#mode_list .mode_box .mode_item').length) {
					waterfall('html');
					return false;
				}
				curPage = page;
				modeType = $('#mode_list .mode_type .type_option ul li:first').data('type');
				$('#mode_list .mode_box .mode_box_container').html('');
				getModeData(modeType);
			});
			// 选择分类
			$('#mode_list .type_option li').on('click', function () {
				usable = true;
				let type = $(this).data('type');
				modeType = $(this).data('type');
				$('#mode_list .form_input').val('')
				getModeData(type);
			});
			$('#mode_list .form_input').on('keydown', function (e) {
				let $Value = $(this).val()
				let key = window.event ? e.keyCode : e.which
				if (key == 13) {
					// 回车键
					usable = true;
					modeType = $('#mode_list .mode_type').attr('data-type');
					getModeData(modeType);
				}
			});
			$('#mode_list .search_btn').on('click', function () {
				usable = true;
				modeType = $('#mode_list .mode_type').attr('data-type');
				getModeData(modeType);
			});
			// 弹窗关闭
			$('#mode_list .close').on('click', () => {
				$('#mode_list').fadeOut();
			});
			// 添加插件
			$('#mode_list .mode_box').on('click', '.mode_item .item_mask a', function () {
				setProgressRate();
				let obj = $(this).parents('.mode_item');
				if (obj.find('.item_permit').length) return false;
				let type = obj.data('type');
				let mode = obj.data('mode');
				let page = $('#plugins_visual input[name=Page]').val();
				let draftsId = $('#plugins_visual input[name=DraftsId]').val();
				let pagesId = $('#plugins_visual input[name=PagesId]').val();
				let id = 0;
				if (page == 'landing_page' || page == 'cod_page') {
					id = $('#plugins_visual input[name=AssociationId]').val();
				}
				$('#mode_list .close').trigger('click');
				$('#plugins_visual .main_bar .loading_mask').fadeIn();
				$('#plugins_visual .tool_bar .loading_mask').fadeIn();
				save_visual_form(
					'',
					() => {
						$.post('/manage/view/visual-v2/plugins-add/', { DraftsId: draftsId, PagesId: pagesId, Page: page, Id: id, Type: type, Mode: mode }, (data) => {
							if (data.ret == 1) {
								_init = false;
								$('#plugins_visual input[name=CurrentPluginsId]').val(data.msg);
								setTimeout(() => {
									document.getElementById('plugins_iframe_themes').contentWindow.location.reload();
								}, 500);
							}
						}, 'json');
					});
			});
			// 删除插件
			$('#plugins_visual .tool_bar').on('click', '.fixed_global .plugins_delete', function () {
				let pid = $(this).parents('.fixed_global').data('plugins');
				if (!pid) return false;
				let draftsId = $('#plugins_visual input[name=DraftsId]').val();
				let pagesId = $('#plugins_visual input[name=PagesId]').val();
				let page = $('#plugins_visual input[name=Page]').val();
				let id = 0;
				//单个页面模式
				let _singlePage = ['landing_page', 'cod_page'];
				if ($.inArray(page, _singlePage) != -1) {
					id = $('#plugins_visual input[name=AssociationId]').val();
				}
				let params = {
					'title': lang_obj.global.del_confirm,
					'confirmBtn': lang_obj.global.del,
					'confirmBtnClass': 'btn_warn'
				};
				global_obj.win_alert(params, () => {
					$.post('/manage/view/visual-v2/plugins-del/', { DraftsId: draftsId, PagesId: pagesId, Pages: page, Id: id, PId: pid }, (data) => {
						if (data.ret == 1) {
							// 删除插件卡
							$(this).parents('.fixed_global').removeClass('fixing');
							setTimeout(() => {
								$(this).parents('.fixed_global').remove();
							}, 500);
							// 删除导航卡
							$(`#plugins_visual .tool_bar .menu_main .menu_item[data-fixed-plugins=${pid}]`).remove();
							// 删除iframe模块
							let pluginsContent = $('#plugins_iframe_themes').contents().find(`*[data-visual-id='${pid}']`).parent('.visual_plugins_container');
							pluginsContent.removeClass('cur_plugins');
							pluginsContent.removeClass('cur_hover_plugins');
							pluginsContent.find('.plugins_type_box').remove();
							pluginsContent.find('.operate_item_box').remove();
							$('#plugins_iframe_themes').contents().find(`*[data-visual-id=${pid}]`).remove();
						}
					}, 'json');
				}, 'confirm');
			});
			// 翻页
			let timer = null;
			let start = new Date();
			let limit = 1000;  // 1s间隔执行一次     
			$('#mode_list .mode_box').on('scroll', function () {
				if (timer) clearTimeout(timer);
				let cur = new Date();
				let scrollFunc = () => {
					let top = $(this).scrollTop();
					let height = $(this).height();
					let scrollHeight = $(this)[0].scrollHeight;
					let loadHeight = 1200;
					let container = $('#mode_list .mode_box .mode_box_container');
					if (scrollHeight > 5000) loadHeight = 2400;
					if (scrollHeight - (top + height) < loadHeight) {
						if (usable == false) return false;
						let page = parseInt($(this).attr('page'));
						let maxPage = parseInt($(this).attr('max-page'));
						if (page >= maxPage) {
							if (!container.find('.last_item').length) {
								container.append('<div class="mode_item last_item">' + lang_obj.manage.view.no_data + '<div class="item_image"><img src="' + $('img.loading_img').attr('src') + '"></div></div>');
								waterfall('append');
							}
							return false;
						}
						page = page + 1;
						$(this).attr('page', page);
						getModeData(modeType, page);
					}
				}
				if (cur - start >= limit) {
					scrollFunc();
					start = cur;
				} else {
					timer = setTimeout(scrollFunc, 300);
				}
			});
			// 响应式
			let resizeTimer = null;
			$(window).on('resize', () => {
				if (resizeTimer) clearTimeout(resizeTimer);
				resizeTimer = setTimeout(function () {
					waterfall('html');
				}, 500);
			});
		}
		//通过分别的参数 确认当前是什么页面
		var check_page_from = function (Module, Action) {
			let Page = Action,
				Name = lang_obj.manage.view.page_ary[Module][Action];

			let Back = ''
			if (Module == 'index' && Action == 'index') {
				Page = 'index';
				Back = 'index';
			} else if (Action == 'products') {
				Page = 'list';
				Back = 'list';
			} else if (Module == 'article' && Action == 'index') {
				Page = Module;
				Back = Module;
			} else if (Module == 'products' && Action == 'goods') {
				Page = 'goods';
				Back = 'goods';
			} else if (Module == 'app' && Action == 'landing_page') {
				Page = 'landing_page';
				Back = 'landing_page';
			} else if (Module == 'app' && Action == 'cod_page') {
				Page = 'cod_page';
				Back = 'cod';
			} else if (Module == 'tracking' && Action == 'index') {
				Page = 'cod_page';
				Back = 'cod';
			} else if (Module == 'blog' && Action == 'index') {
				Page = 'blog';
				Back = 'blog';
			} else if (Module == 'blog' && Action == 'blog-detail') {
				Page = 'blog-detail';
				Back = 'blog-detail';
			} else if (Module == 'cases' && Action == 'index') {
				Page = 'cases';
				Back = 'cases';
			} else if (Module == 'cases' && Action == 'cases-detail') {
				Page = 'cases-detail';
				Back = 'cases-detail';
			} else if (Module == 'news' && Action == 'index') {
				Page = 'news';
				Back = 'news';
			} else if (Module == 'news' && Action == 'news-detail') {
				Page = 'news-detail';
				Back = 'news-detail';
			} else if (Module == 'download' && Action == 'index') {
				Page = 'download';
				Back = 'download';
			} else {
				Page = '';
			}

			$return = {
				0: Page,
				1: Name,
				2: Back
			};
			return $return;
		}
		// 请求右侧编辑内容
		var visual_edit_tool_bar = ($page, $DraftsId, $PagesId, $AssociationId) => {
			let $obj = $('#plugins_visual .tool_bar .tool_bar_content'),
				id = $AssociationId,//$('#plugins_visual input[name=AssociationId]').val(),
				pagesId = $PagesId,//$('#plugins_visual input[name=PagesId]').val(),
				currentPluginsId = $('#plugins_visual input[name=CurrentPluginsId]').val();
			$.post('/manage/view/edit-tool-bar?handler=JsonOutput', { 'page': $page, 'DraftsId': $DraftsId, 'id': id, 'PagesId': pagesId }, (result) => {
				if (result.ret == 1 && result.msg.Html) {
					$obj.empty().append(result.msg.Html)
					// 页面加载好赋值
					$('#plugins_visual input[name=DraftsId]').val($DraftsId);
					$('#plugins_visual input[name=PagesId]').val($PagesId);
					$('#plugins_visual input[name=AssociationId]').val($AssociationId);
					$('#plugins_visual input[name=Page]').val($page);

					$('#plugins_visual .tool_bar .loading_mask').fadeOut()
					if (currentPluginsId > 0) {
						$(`#plugins_visual .tool_bar .tool_bar_menu .menu_item[data-fixed-plugins=${currentPluginsId}]`).trigger('click')
					}
					if (result.msg.ReplaceData) {
						window.VisualReplaceData = JSON.parse(result.msg.ReplaceData);
					} else {
						window.VisualReplaceData = [];
					}
					checkHeightStutas()
					reload_init();
				}
			}, 'json');
		}
		//iframe发生改变
		var iframe_change_init = function () {
			
			// iframe准备好
			var passiveSupported = false;
			try {
				var options = Object.defineProperty({}, "passive", {
					get: function () {
						passiveSupported = true;
					}
				});
			
				window.addEventListener("pageReady", null, options);
			} catch (err) { }
			function fn() {
				console.log("fn")
			}
			window.addEventListener('pageReady', function (event) {
				console.log("pageReady");
				let pageReadySuccessCustomEvent = new CustomEvent('pageReadySuccess');
				$('#plugins_iframe_themes')[0].contentWindow.dispatchEvent(pageReadySuccessCustomEvent);
				if (!$('#plugins_iframe_themes').contents().find('body').hasClass('visual_edit_mode')) { $('#plugins_iframe_themes').contents().find('body').addClass('visual_edit_mode') }
				let Module = event.detail.Module,
					Action = event.detail.Action,
					AssociationId = event.detail.AssociationId,
					DraftsId = event.detail.DraftsId,
					PagesId = event.detail.PagesId,
					PageInfo = check_page_from(Module, Action),
					currentPluginsId = $('#plugins_visual input[name=CurrentPluginsId]').val();
				Page = PageInfo[0];
				Name = PageInfo[1];
				Back = PageInfo[2];
				if (Action == 'products') Page = 'list';
				if (Module == 'article' && Action == 'index') {
					Page = Module;
					let ArticleName = $(`#plugins_visual .go_select .page_select_box .menu_item a.select_item[data-pages-id=${PagesId}]`).text();
					if (ArticleName) Name = ArticleName;
				} else if (Action == 'goods' || Action == 'products') {
					let proName = $(`#plugins_visual .go_select .page_select_box .menu_item a.select_item[data-pages-id=${PagesId}]`).text();
					if (proName) Name = proName;
				}
				if (!Page) Page = 'Other';
				if ($('#plugins_visual input[name=Page]').val() != Page) _init = false;
				//切换页面 请求对应页面的模板
				view_obj.page_change_add_mode(DraftsId, Page);
				// 切换页面卡着色
				$('#plugins_visual .top_bar .go_select .show_page_item').html(Name);
				$('#plugins_visual .top_bar .go_select .sub_box .menu_item').removeClass('current');
				if (Page == 'goods' || Page == 'article') {
					$(`#plugins_visual .top_bar .go_select .sub_box .menu_item .select_item[data-pages-id=${PagesId}]`).parents('.menu_item').addClass('current');
				}
				//返回按钮赋值
				if (Module == 'app') {
					$('#plugins_visual .go_home').attr('href', '/manage/plugins/' + Back.replace('_', '-'));
				}
				// 跳回当前着色插件
				if (_init === true && currentPluginsId > 0) {
					setTimeout(() => {  // C端需要500ms前置准备
						let customEvent = new CustomEvent('visualModuleCurrent', { detail: { pid: currentPluginsId } });
						$('#plugins_iframe_themes')[0].contentWindow.dispatchEvent(customEvent);
					}, 500);
				}
				setProgressRate(1);
				// 初始加载，渲染工具卡
				if (_init === false) {
					_init = true;
					visual_edit_tool_bar(Page, DraftsId, PagesId, AssociationId);
				}
				// iframe页面淡出
				setTimeout(() => {
					$('#plugins_visual .main_bar .loading_mask').fadeOut();
					// 在产品模块,没有产品显示弹窗
					if ((Page == 'list' || Page == 'goods') && $('.view_blanktip_box').length) $('.view_blanktip_box').fadeIn();
				}, 500);
			}, fn, passiveSupported ? { passive: true } : false);
		}
		// 工具栏弹窗
		var fiexd_global = (obj) => {
			let plugins = obj.data('fixed-plugins');
			if (!plugins) return false;
			let fiexd = $(`#plugins_visual .tool_bar .tool_bar_fixed .fixed_global[data-plugins=${plugins}]`);
			setProgressRate(1);

			let type = obj.data('type'),
				pluginsId = obj.data('fixed-plugins'),
				$page = $('#plugins_visual input[name=Page]').val(),
				$DraftsId = $('#plugins_visual input[name=DraftsId]').val(),
				id = parseInt($('#plugins_visual input[name=AssociationId]').val()),
				pagesId = $('#plugins_visual input[name=PagesId]').val(),
				currentPluginsId = $('#plugins_visual input[name=CurrentPluginsId]').val();
			if (isNaN(id)) id = 0;
			//debugger;
			if (!fiexd.length) {
				$('#plugins_visual .tool_bar .loading_mask').css('z-index', 999).fadeIn();
				$.post('/manage/view/visual-v2/get-edit-plugins-tool-bar', { 'page': $page, 'DraftsId': $DraftsId, 'id': id, 'PagesId': pagesId, 'PId': pluginsId, 'Type': type }, (result) => {
				
					if (result) {
						$(`#plugins_visual .tool_bar .tool_bar_fixed`).append(result);
						let fiexd = $(`#plugins_visual .tool_bar .tool_bar_fixed .fixed_global[data-plugins=${plugins}]`);
						let size = fiexd.siblings('.fixing').length;
						fiexd.addClass('fixing').css('z-index', size + 1);
						reload_init();
						$('#plugins_visual .tool_bar .loading_mask').css('z-index', 999).fadeOut();
						setProgressRate(1);
					}
				})
			} else {
				let size = fiexd.siblings('.fixing').length;
				fiexd.addClass('fixing').css('z-index', size + 1);
			}
		}
		// 字体选择公共库
		var font_select_init = (fontName, inputName) => {
			// 拉取字体库
			let load_init = (keyword = '', page = 1) => {
				if (disabled) return false;
				disabled = true;
				$.post('/manage/view/visual-v2/font-load', { Page: page, Keyword: keyword }, (data) => {
					disabled = false;
					if (data.ret == 1) {
						fontObj.find('.font_box .font_loading').remove();
						let html = link = '';
						for (let key in data.msg.data) {
							let className = data.msg.data[key]['font'] == fontName ? 'current' : '';
							html += `<div class="font_item ${className}" style="font-family: ${data.msg.data[key]['font']};">${data.msg.data[key]['font']}</div>`;
							link += data.msg.data[key]['html'];
						}
						fontObj.find('.font_box').attr({ 'page': page, 'max-page': data.msg.maxPage });
						fontObj.find('.font_html').append(link);
						page == 1 ? fontObj.find('.font_box').html(html) : fontObj.find('.font_box').append(html);
						if (page < data.msg.maxPage) fontObj.find('.font_box').append('<div class="font_loading"></div>');
					} else if (data.ret == 2) {
						fontObj.find('.font_box').html(data.msg);
					}
				}, 'json');
			}
			let fontObj = $('.fixed_global.font_select');
			let disabled = false;
			fontObj.find('.font_current span').text(fontName).siblings('input').val(inputName);
			if (fontObj.find('.font_box .font_item').length) {
				fontObj.find('.font_box').scrollTop(0);
				fontObj.find('.font_box .font_item').removeClass('current');
				fontObj.find(`.font_box .font_item[style*=${fontName}]`).addClass('current');
			} else {
				load_init();
			}
			// 赋值
			fontObj.off().on('click', '.font_box .font_item', function () {
				$(this).addClass('current').siblings().removeClass('current');
				fontObj.find('.font_current span').text($(this).text());
			});
			// 弹窗关闭
			fontObj.find('.g_btn_simple').off().on('click', function () {
				$(this).parents('.fixed_global').removeClass('fixing');
			});
			// 弹窗保存
			fontObj.find('.g_btn_main').off().on('click', function () {
				let value = fontObj.find('.font_current span').text();
				let input = fontObj.find('.font_current input').val();
				$('#plugins_visual .tool_bar').find(`input[name='${input}']`).val(value).trigger('change');
				$(this).parents('.fixed_global').removeClass('fixing');
			});
			// 搜索字体
			fontObj.find('.search_btn').off('click').on('click', function () {
				fontObj.find('.font_box').scrollTop(0);
				let keyword = $(this).siblings('.form_input').val();
				$(this).parents('.search_box').siblings('.font_box').attr('page', 1);
				load_init(keyword);
			});
			fontObj.find('.search_box .form_input').on('keyup', function (event) {
				if (event.keyCode == 13) fontObj.find('.search_btn').trigger('click');
			});
			// 下拉加载
			let timer = null;
			let start = new Date();
			let limit = 1000;  // 1s间隔执行一次        
			fontObj.find('.font_box').on('scroll', function () {
				if (timer) clearTimeout(timer);
				let cur = new Date();
				let scrollFunc = () => {
					let top = $(this).scrollTop();
					let height = $(this).height();
					let scrollHeight = $(this)[0].scrollHeight;
					if (scrollHeight - (top + height) < 220) {
						let keyword = $(this).siblings('.search_box').find('.form_input').val();
						let page = parseInt($(this).attr('page'));
						let maxPage = parseInt($(this).attr('max-page'));
						if (page >= maxPage) return false;
						page = page + 1;
						$(this).attr('page', page);
						load_init(keyword, page);
					}
				}
				if (cur - start >= limit) {
					scrollFunc();
					start = cur;
				} else {
					timer = setTimeout(scrollFunc, 300);
				}
			});
		}
		// 效果选择公共库
		var effect_select_init = (effectName, inputName, options) => {
			let effectObj = $('.fixed_global.effect_select');
			// 初始化列表
			if (options.length > 0) {  // 有范围限制
				effectObj.find('.effect_box .effect_item').hide();
				for (let key in options) {
					effectObj.find(`.effect_box .effect_item[data-value=${options[key]}]`).show();
				}
			} else {
				effectObj.find('.effect_box .effect_item').show();
			}
			effectObj.find('.effect_box .effect_item').removeClass('current').siblings(`[data-value=${effectName}]`).addClass('current');
			effectObj.find('.effect_box .effect_item').off().on('click', function () {
				$(this).addClass('current').siblings().removeClass('current');
			});
			// 弹窗关闭
			effectObj.find('.g_btn_simple').off().on('click', function () {
				$(this).parents('.fixed_global').removeClass('fixing');
			});
			// 弹窗保存
			effectObj.find('.g_btn_main').off().on('click', function () {
				let value = effectObj.find('.effect_box .effect_item.current').data('value');
				let text = effectObj.find('.effect_box .effect_item.current span').text();
				$('#plugins_visual .tool_bar').find(`input[name='${inputName}']`).val(value).trigger('change').siblings('.effect_name').find('span').text(text).siblings('.effect_icon').attr('class', 'effect_icon').addClass(`effect_icon_${value}`);
				$(this).parents('.fixed_global').removeClass('fixing');
			});
		}
		// 分类选择公共库
		var category_select_init = (inputName, inputValue) => {
			// 拉取分类库
			let load_init = (keyword = '', page = 1) => {
				if (disabled) return false;
				disabled = true;
				$.post('/manage/view/visual-v2/category-load', { Page: page, Keyword: keyword }, (data) => {
					disabled = false;
					if (data.ret == 1) {
						let html = '';
						for (let key in data.msg.data) {
							let picPath = '';
							let className = data.msg.data[key]['CateId'] == inputValue ? 'current' : '';
							if (data.msg.data[key]['PicPath']) {
								picPath = `style="background-image: url(${data.msg.data[key]['PicPath']});background-size: cover;background-position: center center;"`;
							}
							html += `<div class="category_item ${className}" data-id="${data.msg.data[key]['CateId']}">`;
							html += `<div class="category_image" ${picPath}></div>`;
							html += `<div class="category_name">${data.msg.data[key]['Category_en']}</div>`;
							html += '</div>';
						}
						categoryObj.find('.category_box').attr({ 'page': page, 'max-page': data.msg.maxPage });
						page == 1 ? categoryObj.find('.category_box').html(html) : categoryObj.find('.category_box').append(html);
					} else if (data.ret == 2) {
						categoryObj.find('.category_box').html(data.msg);
					}
				}, 'json');
			}
			let categoryObj = $('.fixed_global.category_select');
			let disabled = false;
			if (categoryObj.find('.category_box .category_item').length) {
				categoryObj.find('.category_box').scrollTop(0);
				categoryObj.find('.category_box .category_item').removeClass('current');
				categoryObj.find(`.category_box .category_item[data-id='${inputValue}']`).addClass('current');
			} else {
				load_init();
			}
			// 赋值
			categoryObj.off().on('click', '.category_box .category_item', function () {
				$(this).addClass('current').siblings().removeClass('current');
			});
			// 弹窗关闭
			categoryObj.find('.g_btn_simple').off().on('click', function () {
				$(this).parents('.fixed_global').removeClass('fixing');
			});
			// 弹窗保存
			categoryObj.find('.g_btn_main').off().on('click', function () {
				let value = categoryObj.find('.category_box .category_item.current').data('id');
				let name = categoryObj.find('.category_box .category_item.current .category_name').text();
				let style = categoryObj.find('.category_box .category_item.current .category_image').attr('style');
				if (!style) style = '';
				$('#plugins_visual .tool_bar').find(`input[name='${inputName}']`).attr({ 'category': name, 'style': style }).val(value).trigger('change');
				$(this).parents('.fixed_global').removeClass('fixing');
			});
			// 搜索分类
			categoryObj.find('.search_btn').off('click').on('click', function () {
				categoryObj.find('.category_box').scrollTop(0);
				let keyword = $(this).siblings('.form_input').val();
				$(this).parents('.search_box').siblings('.category_box').attr('page', 1);
				load_init(keyword);
			});
			// 刷新
			categoryObj.find('.g_btn.refresh').on('click', function () {
				categoryObj.find('.category_box').attr('page', 1).scrollTop(0);
				categoryObj.find('.search_box .form_input').val('');
				load_init();
			});
			// 下拉加载
			let timer = null;
			let start = new Date();
			let limit = 1000;  // 1s间隔执行一次        
			categoryObj.find('.category_box').on('scroll', function () {
				if (timer) clearTimeout(timer);
				let cur = new Date();
				let scrollFunc = () => {
					let top = $(this).scrollTop();
					let height = $(this).height();
					let scrollHeight = $(this)[0].scrollHeight;
					if (scrollHeight - (top + height) < 220) {
						let keyword = $(this).siblings('.search_box').find('.form_input').val();
						let page = parseInt($(this).attr('page'));
						let maxPage = parseInt($(this).attr('max-page'));
						if (page >= maxPage) return false;
						page = page + 1;
						$(this).attr('page', page);
						load_init(keyword, page);
					}
				}
				if (cur - start >= limit) {
					scrollFunc();
					start = cur;
				} else {
					timer = setTimeout(scrollFunc, 300);
				}
			});
		}
		// 页尾添加内容选择公共库
		var footer_content_init = (obj, pid, func) => {
			let contentObj = $('.fixed_global.footer_content');
			let blocks = '';
			// 着色
			contentObj.find('.content_box .content_item').off().on('click', function () {
				blocks = $(this).data('value');
				$(this).addClass('current').siblings().removeClass('current');
			});
			contentObj.find('.content_box .content_item:first').trigger('click');
			// 弹窗关闭
			contentObj.find('.g_btn_simple').off().on('click', function () {
				$(this).parents('.fixed_global').removeClass('fixing');
			});
			// 弹窗保存
			contentObj.find('.g_btn_main').off().on('click', function () {
				$(this).parents('.fixed_global').removeClass('fixing');
				$.post('/manage/view/visual-v2/blocks-add', { PId: pid, Blocks: blocks }, (data) => {
					if (data.ret == 1) func.apply(obj, [pid, data]);
				}, 'json');
			});
		}
		// 导航选择公共库
		var nav_select_init = (inputName, inputValue, type, tips = '', multi = 0) => {
			// 拉取导航库
			let load_init = (keyword = '', page = 1) => {
				if (disabled) return false;
				disabled = true;
				$.post('/manage/view/visual-v2/nav-load', { Page: page, Keyword: keyword, Type: type }, (data) => {
					disabled = false;
					navObj.find('.fixed_loading_mask').hide();
					if (data.ret == 1) {
						if (page == 1) {
							navObj.find('.nav_box').html('');
						}
						let html = '';
						for (let key in data.msg.data) {
							html += `<div class="nav_item" data-id="${data.msg.data[key]['id']}">`;
							html += `<div class="nav_name">${data.msg.data[key]['name']}</div>`;
							html += '</div>';
						}
						navObj.find('.nav_box').attr({ 'page': page, 'max-page': data.msg.maxPage });
						page == 1 ? navObj.find('.nav_box').html(html) : navObj.find('.nav_box').append(html);
						if (navObj.find('.nav_box .nav_item').length) {
							navObj.find('.nav_box').scrollTop(0);
							navObj.find('.nav_box .nav_item').removeClass('current');
							if (multi) {
								for (k in paramValue) {
									navObj.find(`.nav_box .nav_item[data-id='${paramValue[k]}']`).addClass('current');
								}
							} else {
								navObj.find(`.nav_box .nav_item[data-id='${inputValue}']`).addClass('current');
							}
						}
					} else if (data.ret == 2) {
						navObj.find('.nav_box').html(data.msg);
					}
				}, 'json');
			}
			let navObj = $('.fixed_global.nav_select');
			let disabled = false;
			let paramValue = inputValue.split(',').filter(item => item != '');
			load_init();
			navObj.find('.search_box input[name=expand\\[Keyword\\]]').attr('placeholder', lang_obj.manage.view.nav_slect_placeholder[type])
			navObj.find('.nav_tips_box').remove();
			navObj.find('.nav_box').removeClass('tips_height');
			if (tips && !navObj.find('.nav_tips_box').length) {
				let _tipsBox = `<div class="global_app_tips nav_tips_box"><em></em><span>${tips}</span></div>`;
				navObj.find('.search_box').after(_tipsBox);
				navObj.find('.nav_box').addClass('tips_height');
			}
			navObj.find('.fixed_loading_mask').show();
			// 赋值
			navObj.off().on('click', '.nav_box .nav_item', function () {
				if (multi) {
					let id = parseInt($(this).data('id'));
					if ($(this).hasClass('current')) {  // 取消勾选
						paramValue = paramValue.filter(item => item != id);
					} else {  // 勾选
						if (paramValue.length >= parseInt(multi)) {
							global_obj.win_alert_auto_close(lang_obj.manage.sales.beyondNumber + multi, 'await', 2000, '8%', 0);
							return false;
						}
						paramValue.push(id);
					}
					$(this).toggleClass('current');
				} else {
					$(this).addClass('current').siblings().removeClass('current');
				}
			});
			// 弹窗关闭
			navObj.find('.g_btn_simple').off().on('click', function () {
				$(this).parents('.fixed_global').removeClass('fixing');
			});
			// 弹窗保存
			navObj.find('.g_btn_main').off().on('click', function () {
				if (multi) {
					let value = paramValue.join(',');
					let _name = '';
					for (k in paramValue) {
						_name += navObj.find('.nav_box .nav_item.current[data-id=' + paramValue[k] + '] .nav_name').text() + '、';
					}
					_name = _name.substr(0, _name.length - 1);
					$('#plugins_visual .tool_bar').find(`input[name='${inputName}']`).attr('nav', _name).attr('data-name', _name).val(value).trigger('change');

				} else {
					let value = navObj.find('.nav_box .nav_item.current').data('id');
					let name = navObj.find('.nav_box .nav_item.current .nav_name').text();
					$('#plugins_visual .tool_bar').find(`input[name='${inputName}']`).attr('nav', name).attr('data-name', name).val(value).trigger('change');
				}
				$(this).parents('.fixed_global').removeClass('fixing');
			});
			// 搜索导航
			navObj.find('.search_btn').off('click').on('click', function () {
				navObj.find('.category_box').scrollTop(0);
				let keyword = $(this).siblings('.form_input').val();
				$(this).parents('.search_box').siblings('.category_box').attr('page', 1);
				load_init(keyword);
			});
			navObj.find('.search_box .form_input').on('keyup', function (event) {
				if (event.keyCode == 13) navObj.find('.search_btn').trigger('click');
			});
			// 刷新
			navObj.find('.g_btn.refresh').on('click', function () {
				navObj.find('.category_box').attr('page', 1).scrollTop(0);
				navObj.find('.search_box .form_input').val('');
				load_init();
			});
			// 下拉加载
			let timer = null;
			let start = new Date();
			let limit = 1000;  // 1s间隔执行一次        
			navObj.find('.nav_box').on('scroll', function () {
				if (timer) clearTimeout(timer);
				let cur = new Date();
				let scrollFunc = () => {
					let top = $(this).scrollTop();
					let height = $(this).height();
					let scrollHeight = $(this)[0].scrollHeight;
					if (scrollHeight - (top + height) < 220) {
						let keyword = $(this).siblings('.search_box').find('.form_input').val();
						let page = parseInt($(this).attr('page'));
						let maxPage = parseInt($(this).attr('max-page'));
						if (page >= maxPage) return false;
						page = page + 1;
						$(this).attr('page', page);
						load_init(keyword, page);
					}
				}
				if (cur - start >= limit) {
					scrollFunc();
					start = cur;
				} else {
					timer = setTimeout(scrollFunc, 300);
				}
			});
		}
		//优惠券下拉公共库
		var coupon_select_init = () => {
			//模拟下拉插件		
			$('body').click(function (e) {//关闭
				$('.simulate_select_box .select').removeClass('focus').siblings('.option_box').hide();
			});
			$('.simulate_select_box').click(function (e) {//阻止事件冒泡
				e.stopPropagation();
			}).on('click', '.select', function () {//点击打开
				$(this).addClass('focus').siblings('.option_box').show();
			}).on('click', '.btn_refresh', function () {//刷新
				var $this = $(this),
					$box = $this.parents('.simulate_select_box'),
					$parent = $box.find('.option_box'),
					$perpage = parseInt($parent.find('.btn_load_more').attr('data-per-page'));
				$this.addClass('refreshing');
				$.get('/manage/sales/operation-activities/load-more?page=1&per-page=' + $perpage, function (data) {
					if (data.ret == 1) {
						$parent.find('.option_list .item').remove();
						$parent.find('.btn_load_more').attr('data-page', 2).before(data.msg.html);
						//加上选择效果 失效的删掉
						$box.find('.select .selected input').each(function () {
							var $item = $parent.find('.item[data-value="' + $(this).val() + '"]');
							$item.addClass('current');
							if ($item.hasClass('disabled')) {
								$(this).parent().remove();
							}
						});
						if (!$box.find('.select .selected input').length) $box.find('.select .placeholder').show();
						$parent.find('.btn_load_more').css('display', (data.msg.pageCount > 1 ? 'block' : 'none'));
						if (data.msg.total > 0) {
							$parent.find('.option_list').show();
							$parent.find('.no_data').hide();
						} else {
							$parent.find('.option_list').hide();
							$parent.find('.no_data').show();
						}
						$this.removeClass('refreshing');
					}
				}, 'json');
			}).on('click', '.option_list .item:not(.disabled)', function () {
				let $this = $(this),
					$parent = $this.parent(),
					$box = $this.parents('.simulate_select_box'),
					$type = $box.attr('data-type'),
					$val = $this.attr('data-value'),
					$html = '',
					$is_used = $this.parents('.input').find('input[name="IsUsed"]');
				$is_used.prop('checked', false).parents('.input_checkbox_box').removeClass('checked');
				if ($type == 'checkbox') {
					$(this).addClass('current');
					if (!$box.find('.select .selected[data-value="' + $val + '"]').length) {
						$html = '<span class="selected" data-value="' + $val + '">' + $val + '<input type="hidden" name="' + $parent.attr('data-name') + '[]' + '" value="' + $val + '"><i></i></span>';
						$box.find('.select').append($html).find('.placeholder').hide();
					}
				} else if ($type == 'select') {
					$(this).addClass('current').siblings().removeClass('current');
					$box.find('.select .selected').remove();
					$html = '<span class="selected" data-value="' + $val + '">' + $val + '<input type="hidden" name="' + $parent.attr('data-name') + '" value="' + $val + '"><i></i></span>';
					$box.find('.select').append($html).find('.placeholder').hide();
				}
				save_visual_form(
					$this,
					() => data_init.apply(this)
				);
			}).on('click', '.btn_load_more', function () {
				//加载更多
				var $this = $(this),
					$box = $this.parents('.simulate_select_box'),
					$page = parseInt($this.attr('data-page')),
					$perpage = parseInt($this.attr('data-per-page'));
				$.get('/manage/sales/operation-activities/load-more?page=' + $page + '&per-page=' + $perpage, function (data) {
					if (data.ret == 1) {
						$this.attr('data-page', $page + 1);
						$this.before(data.msg.html);
						//加上选择效果 失效的删掉
						$box.find('.select .selected input').each(function () {
							var $item = $box.find('.option_list .item[data-value="' + $(this).val() + '"]');
							$item.addClass('current');
							if ($item.hasClass('disabled')) {
								$(this).parent().remove();
							}
						});
						if (!$box.find('.select .selected input').length) $box.find('.select .placeholder').show();
						if (data.msg.page >= data.msg.pageCount) { //最后一页
							global_obj.win_alert_auto_close(lang_obj.manage.sales.lastpage, 'await', 1000, '8%');
							$this.fadeOut();
						}
					}
				}, 'json');
			}).on('click', '.select .selected i', function () {
				//删除选项
				var $this = $(this),
					$val = $this.parent().find('input').val(),
					$box = $this.parents('.simulate_select_box');
				$this.parent().remove();
				if (!$box.find('.select .selected').length) $box.find('.select .placeholder').show();
				$box.find('.option_list .item[data-value="' + $val + '"]').removeClass('current');
				return false; //阻止冒泡
			});
			$.post('/manage/sales/operation-activities/update-coupon-status', '', function () {
				$('.simulate_select_box .btn_refresh').click();
			}, 'json');
		}
		// 数据弹窗选择公共库
		var custom_page_select_init = (inputValue, type, tips = '', multiNumber = 0, multiple = 0, callback, beforeFun) => {
			// 拉取导航库
			let load_init = (keyword = '', page = 1) => {
				if (disabled) return false;
				disabled = true;
				$.post('/manage/view/visual-v2/nav-load', { Page: page, Keyword: keyword, Type: type }, (data) => {
					disabled = false;
					navObj.find('.fixed_loading_mask').hide();
					if (data.ret == 1) {
						if (page == 1) {
							navObj.find('.custom_page_box').html('');
						}
						let html = '';
						for (let key in data.msg.data) {
							html += `<div class="custom_page_item" data-id="${data.msg.data[key]['id']}">`;
							html += `<div class="custom_page_name">${data.msg.data[key]['name']}</div>`;
							html += '</div>';
						}
						navObj.find('.custom_page_box').attr({ 'page': page, 'max-page': data.msg.maxPage });
						page == 1 ? navObj.find('.custom_page_box').html(html) : navObj.find('.custom_page_box').append(html);
						if (navObj.find('.custom_page_box .custom_page_item').length) {
							navObj.find('.custom_page_box').scrollTop(0);
							navObj.find('.custom_page_box .custom_page_item').removeClass('current');
							if (multiple) {
								for (k in paramValue) {
									navObj.find(`.custom_page_box .custom_page_item[data-id='${paramValue[k]}']`).addClass('current');
								}
							} else {
								navObj.find(`.custom_page_box .custom_page_item[data-id='${inputValue}']`).addClass('current');
							}
						}
					} else if (data.ret == 2) {
						navObj.find('.custom_page_box').html(data.msg);
					}
				}, 'json');
			}
			let navObj = $('.fixed_global.custom_page_select');
			let disabled = false;
			let paramValue = inputValue ? inputValue.split(',').filter(item => item != '') : [];
			load_init();
			navObj.find('.search_box input[name=expand\\[Keyword\\]]').attr('placeholder', lang_obj.manage.view.nav_slect_placeholder[type])
			navObj.find('.nav_tips_box').remove();
			navObj.find('.custom_page_box').removeClass('tips_height');
			if (tips && !navObj.find('.nav_tips_box').length) {
				let _tipsBox = `<div class="global_app_tips nav_tips_box"><em></em><span>${tips}</span></div>`;
				navObj.find('.search_box').after(_tipsBox);
				navObj.find('.custom_page_box').addClass('tips_height');
			}
			navObj.find('.fixed_loading_mask').show();
			// 赋值
			navObj.off().on('click', '.custom_page_box .custom_page_item', function () {
				if (multiple) {
					let id = parseInt($(this).data('id'));
					if ($(this).hasClass('current')) {  // 取消勾选
						paramValue = paramValue.filter(item => item != id);
					} else {  // 勾选
						if (multiNumber > 0 && paramValue.length >= parseInt(multiNumber)) {
							global_obj.win_alert_auto_close(lang_obj.manage.sales.beyondNumber + multiNumber, 'await', 2000, '8%', 0);
							return false;
						}
						paramValue.push(id);
					}
					$(this).toggleClass('current');
				} else {
					$(this).addClass('current').siblings().removeClass('current');
				}
				if ($.isFunction(beforeFun)) {
					if (beforeFun(paramValue) === false) {
						return false;
					}
				}
			});
			// 弹窗关闭
			navObj.find('.g_btn_simple').off().on('click', function () {
				$(this).parents('.fixed_global').removeClass('fixing');
			});
			// 弹窗保存
			navObj.find('.g_btn_main').off().on('click', function () {
				if ($.isFunction(callback)) callback(paramValue);
			});
			// 搜索导航
			navObj.find('.search_btn').off('click').on('click', function () {
				navObj.find('.category_box').scrollTop(0);
				let keyword = $(this).siblings('.form_input').val();
				$(this).parents('.search_box').siblings('.category_box').attr('page', 1);
				load_init(keyword);
			});
			navObj.find('.search_box .form_input').on('keyup', function (event) {
				if (event.keyCode == 13) navObj.find('.search_btn').trigger('click');
			});
			// 刷新
			navObj.find('.g_btn.refresh').on('click', function () {
				navObj.find('.category_box').attr('page', 1).scrollTop(0);
				navObj.find('.search_box .form_input').val('');
				load_init();
			});
			// 下拉加载
			let timer = null;
			let start = new Date();
			let limit = 1000;  // 1s间隔执行一次        
			navObj.find('.custom_page_box').on('scroll', function () {
				if (timer) clearTimeout(timer);
				let cur = new Date();
				let scrollFunc = () => {
					let top = $(this).scrollTop();
					let height = $(this).height();
					let scrollHeight = $(this)[0].scrollHeight;
					if (scrollHeight - (top + height) < 220) {
						let keyword = $(this).siblings('.search_box').find('.form_input').val();
						let page = parseInt($(this).attr('page'));
						let maxPage = parseInt($(this).attr('max-page'));
						if (page >= maxPage) return false;
						page = page + 1;
						$(this).attr('page', page);
						load_init(keyword, page);
					}
				}
				if (cur - start >= limit) {
					scrollFunc();
					start = cur;
				} else {
					timer = setTimeout(scrollFunc, 300);
				}
			});
		}
		// 校对Blocks添加按钮是否显示
		var check_blocks_add_btn = (pid) => {
			let obj = $(`#plugins_visual .fixed_global[data-plugins=${pid}]`);
			if (!obj.find('.plugins_title span').length) return false;
			let size = obj.find('.blocks_item .menu_item').length;
			obj.find('.plugins_title span b:eq(0)').text(size);
			let maxSize = obj.find('.plugins_title span b:eq(1)').text();
			size < maxSize ? obj.find('.plugins_blocks .menu_btn').show() : obj.find('.plugins_blocks .menu_btn').hide();
		}
		// 颜色组件初始化
		var pickr = {};
		var component_color_init = () => {
			// 先删除残留的颜色组件
			if (Object.keys(pickr).length) {
				for (let key in pickr) {
					pickr[key].destroyAndRemove();
					delete pickr[key];
				}
				// 补回删除后的div
				$('.component_color .color_box').each(function () {
					if (!$(this).children('.color_button').length) $(this).append('<div class="color_button"></div>');
				});
			}
			// 实例化颜色组件
			$('.component_color .color_box .color_button').each(function () {
				let id = $(this).parent().data('colpickid');
				let input = $(this).siblings('.color_color');
				let color = input.val();
				let config = { el: this, theme: 'nano', default: color, comparison: false, components: { preview: true, opacity: true, hue: true, interaction: { hex: false, rgba: false, hsva: false, input: true, clear: false, save: false } }, autoReposition: false };
				let obj = new Pickr(config);
				pickr[id] = obj;
				// 事件
				let timer = null;
				obj.on('change', instance => {
					if (timer) clearTimeout(timer);
					timer = setTimeout(() => {
						input.val(instance.toHEXA().toString()).trigger('change');
					}, 1000);
				}).on('show', (color, instance) => {
					let _thisBtn = instance._root.button
					let _winHeight = $(window).height(),
						_thisHeight = $(_thisBtn).offset().top;
					_bottomHeight = _winHeight - _thisHeight
					if (_bottomHeight < 250) {
						$('.pcr-app.visible').css({ 'bottom': _bottomHeight + 12, 'top': 'auto' })
					}
				});
			});
		}
		// 富文本插件初始化
		var ckeditorInit = {};
		var component_richtext_init = () => {
			$('#plugins_visual').find('.richtext_box textarea').each(function () {
				let id = $(this).attr('id');
				if (ckeditorInit[id]) {
					delete CKEDITOR.instances[id];
					$(this).next('div').remove();
				}
				ckeditorInit[id] = true;
				CKEDITOR.replace(id, {
					toolbar: 'visual',
					language: shop_config.manage_language,
					width: '100%',
					height: 150,
					coreStyles_bold: {
						element: 'span',
						styles: { 'font-weight': 'bold' }
					},
					coreStyles_italic: {
						element: 'span',
						styles: { 'font-style': 'italic' }
					}
				});
				// 监听内容被改变
				let componentRichtextTimer = null;
				let disabled = true;
				CKEDITOR.instances[id].on('change', () => {
					if (componentRichtextTimer) clearTimeout(componentRichtextTimer);
					componentRichtextTimer = setTimeout(() => {
						if (disabled) {
							disabled = false;
							// a链接增加类名
							let oldCkeditorData = CKEDITOR.instances[id].getData();
							let ckeditorData = CKEDITOR.instances[id].getData();
							ckeditorData = ckeditorData.replace('a href=', 'a class="visual_cka" href=');
							ckeditorData = ckeditorData.replace('<strong>', '<span style="font-weight: bold;">');
							ckeditorData = ckeditorData.replace('</strong>', '</span>');
							ckeditorData = ckeditorData.replace('<i>', '<span style="font-style: italic;">');
							ckeditorData = ckeditorData.replace('</i>', '</span>');
							ckeditorData = ckeditorData.replace('<b>', '<span style="font-weight: bold;">');
							ckeditorData = ckeditorData.replace('</b>', '</span>');

							if (oldCkeditorData == ckeditorData) {  // 没有替换内容, 不需要重置内容(会失去光标)
								disabled = true;
							} else {
								CKEDITOR.instances[id].setData(ckeditorData, {
									callback: function () {
										setTimeout(() => {
											disabled = true;
										}, 2000);
									}
								});
							}
							// 保存数据
							save_visual_form(
								$(this),
								() => data_init.apply(this)
							);
						}
					}, 1500);
				});
			});
		}
		// 组件联动初始化
		var component_linkage_init = () => {
			$('#plugins_visual').find("*[data-component][data-linkage=true]").each(function () {
				let component = $(this).data('component');
				let func = `component_${component}_linkage_init`;
				try {
					eval(`${func}.apply(this)`);
				} catch (e) {
					console.log(`'${func}' not find`);
				}
			});
		}
		// 组件联动动作函数
		var linkage_change = function (plugins, status, value, filter = '') {
			if (!plugins) return false;
			let pluginsAry = plugins.split(',');
			for (let key in pluginsAry) {
				let filterStatus = false;
				if (filter) {
					let filterAry = filter.split(',');
					if ($.inArray(pluginsAry[key], filterAry) != -1) filterStatus = true;
				}
				let obj = $(this).parents('.fixed_global').find(`*[data-component] *[name*='[${pluginsAry[key]}]']`).parents('*[data-component]');
				((status == value) || filterStatus) ? obj.slideDown() : obj.slideUp();
			}
		}
		// 开关组件联动初始化
		var component_switch_linkage_init = function () {
			let value = $(this).find('input[type=hidden]').val();
			let switchOpen = $(this).data('linkage-1');
			let switchClose = $(this).data('linkage-0');
			linkage_change.apply(this, [switchOpen, 1, value]);
			linkage_change.apply(this, [switchClose, 1, value]);
		}
		// 面板按钮组件联动初始化
		var component_panel_linkage_init = component_switch_linkage_init;
		// 下拉框组件联动初始化
		var component_select_linkage_init = function () {
			let value = $(this).find('.box_select input[type=hidden]:first').val();
			let dropData = $(this).find('.drop_list').attr('data');
			dropData = global_obj.json_encode_data(dropData);
			let valueLinkage = $(this).data(`linkage-${value}`);
			for (let key in dropData) {
				let dropValue = dropData[key].Value;
				let dropLinkage = $(this).data(`linkage-${dropValue}`);
				linkage_change.apply(this, [dropLinkage, dropValue, value, valueLinkage]);
			}
		}
		// 拖动初始化
		var dragsort_init = () => {
			// 导航卡拖动
			$('#plugins_visual .tool_bar .menu_main').dragsort('destroy');
			$('#plugins_visual .tool_bar .menu_main').dragsort({
				dragSelector: '.menu_item',
				dragSelectorExclude: '.item_icon, .item_name, .item_mode, .item_display, .item_copy',
				placeHolderTemplate: '<div class="menu_item"></div>',
				itemSelector: '',
				scrollSpeed: 5,
				dragEnd: function () {
					let iframe = $('#plugins_iframe_themes').contents();
					let pid = $(this).data('fixed-plugins');
					let otherItemId = $(this).prev('.menu_item').length ? $(this).prev('.menu_item').data('fixed-plugins') : $(this).next('.menu_item').data('fixed-plugins');
					let moveType = $(this).prev('.menu_item').length ? 'after' : 'before';
					iframe.find(`*[data-visual-id=${pid}]`).parents('.visual_plugins_container').animate({ opacity: 0 }, 200, function () {
						let html = $(this).clone();
						$(this).remove();
						moveType == 'before' ? iframe.find(`*[data-visual-id=${otherItemId}]`).parents('.visual_plugins_container').before(html) : iframe.find(`*[data-visual-id=${otherItemId}]`).parents('.visual_plugins_container').after(html);
						let top = iframe.find(`*[data-visual-id=${pid}]`).parents('.visual_plugins_container').offset().top;
						iframe.find(`*[data-visual-id=${pid}]`).parents('.visual_plugins_container').animate({ opacity: 1 }, 200);
						iframe.find('body, html').animate({ scrollTop: top });
						save_visual_form();
					});
				}
			});
			// 内容卡拖动
			$('#plugins_visual .tool_bar .plugins_blocks .blocks_item[data-dragsort=1]').dragsort('destroy');
			$('#plugins_visual .tool_bar .plugins_blocks .blocks_item[data-dragsort=1]').dragsort({
				dragSelector: '.menu_item',
				dragSelectorExclude: '.item_icon, .item_name, .item_mode',
				placeHolderTemplate: '<div class="menu_item"></div>',
				itemSelector: '',
				scrollSpeed: 5,
				dragEnd: function () {
					let pluginsId = $(this).data('fixed-plugins');
					let plugins = $(`#plugins_visual .fixed_global[data-plugins=${pluginsId}]`);
					let otherPluginsId = $(this).prev('.menu_item').length ? $(this).prev('.menu_item').data('fixed-plugins') : $(this).next('.menu_item').data('fixed-plugins');
					let otherPlugins = $(`#plugins_visual .fixed_global[data-plugins=${otherPluginsId}]`);
					let moveType = $(this).prev('.menu_item').length ? 'after' : 'before';
					plugins.find('.component_color .color_box').each(function () {  //  先销毁颜色组件, 防止无法再次初始化
						let id = $(this).data('colpickid');
						if (pickr[id]) {
							pickr[id].destroyAndRemove();
							delete pickr[id];
						}
					});
					let html = plugins.clone();
					plugins.remove();
					moveType == 'before' ? otherPlugins.before(html) : otherPlugins.after(html);
					save_visual_form(
						$(this),
						() => data_init.apply(plugins)
					);
					reload_init();
				}
			});
		}
		// 动态插件面板初始化
		var dynamic_select_init = () => {
			dynamicStatus = true;
			$('#plugins_visual .tool_bar_content').on('click', '.dynamic_select_btn', function () {
				if (dynamicStatus == false) return false;
				_this = $(this)
				parentObj = _this.parents('.component_panel'),
					appStatus = parseInt(parentObj.attr('app-used')) ? true : false;
				if (appStatus) {
					dynamicStatus = false;
					let multi = parseInt(parentObj.data('multi')),
						inputObj = parentObj.find('input[type="hidden"]'),
						name = inputObj.attr('name'),
						Value = inputObj.val(),
						type = parentObj.attr('app-type'),
						fixed_obj = parentObj.parents('.fixed_global'),
						PluginsId = parseInt(fixed_obj.data('plugins')),
						params = {
							multi: multi,
							name: name,
							type: type,
							PluginsId: PluginsId,
							Value: Value
						};
					$.post('/manage/view/visual-v2/get-dynamic-html', params, function (result) {
						if (result) {
							$('#visual').append(result);
						}
						dynamicStatus = true;
						dynamic_select_callback();
						dynamic_search_filter_current_init();
					})
					return false;
				}
			})
		}
		var dynamic_select_callback = () => {
			dynamicList = new Array();
			let _Obj = $('#dynamicBox'),
				w = $(window).outerHeight(true),
				b = parseFloat(_Obj.find('.box_title').outerHeight(true)),
				m = parseFloat(_Obj.find('.menu_type').outerHeight(true)),
				t = w - b - m - 150;
			_Obj.find('.dynamicCont').height(t);
			$(window).resize(function () {
				w = $(window).outerHeight(true),
					b = parseFloat(_Obj.find('.box_title').outerHeight(true)),
					m = parseFloat(_Obj.find('.menu_type').outerHeight(true)),
					t = w - b - m - 150;
				_Obj.find('.dynamicCont').height(t);
			})
			$Obj = $('#dynamicBox')
			$Obj.on('click', '.search_form .search_btn, .pagination li:not(.disabled) a', function () {
				url = $(this).attr('href');
				dynamic_select_choice(url);
				return false;
			}).on('click', '.data_box .item', function () {
				var _this = $(this),
					IsChecked = _this.hasClass('checked') ? true : false;
				if (IsChecked) {
					_this.removeClass('checked').find('input').prop('checked', false);
					index = $.inArray(_this.find('input').val(), dynamicList);
					if (index > -1) {
						dynamicList.splice(index, 1);
					}
				} else {
					_this.addClass('checked').find('input').prop('checked', true);
					dynamicList.push(_this.find('input').val());
				}
				dynaminc_total_set_init();

			}).on('click', '.menu_box .menu_all_checkbox', function () {
				setTimeout(() => {
					let IsChecked = $(this).hasClass('checked') ? true : false
					if (IsChecked) {
						$('#dynamicBox').find('.data_box .item:not(.checked)').trigger('click');
					} else {
						$('#dynamicBox').find('.data_box .item.checked').trigger('click');
					}
					dynaminc_total_set_init();
				}, 100);
			}).on('click', '.close_btn i', function () {
				$Obj.remove();
			}).on('click', '.menu_box .box_type_menu .item', function () {
				let _this = $(this),
					type = $(this).find('input').val(),
					isHide = type == 'manual' ? false : true;
				$Obj.find('.menu_box .box_type_menu .item').removeClass('checked').find('input').prop('checked', false);
				$Obj.find('.dynamicCont .box_container').hide();
				_this.addClass('checked').find('input').prop('checked', true);
				$Obj.find('.dynamicCont').find('.box_container[data-type="' + type + '"]').show();
				if (isHide) {
					_this.parents('.menu_box').find('.intelligent_cont').hide();
					$Obj.find('.middle_top_menu .search_box_selected').hide();
				} else {
					_this.parents('.menu_box').find('.intelligent_cont').show();
					$Obj.find('.middle_top_menu .search_box_selected').show();
				}

				if (type == 'intelligent') {
					$Obj.find('input[name="displayNumber"]').attr('notnull', 'notnull');
				} else {
					$Obj.find('.search_btn').trigger('click');
					$Obj.find('input[name="displayNumber"]').removeAttr('notnull');
				}
				$Obj.find('.dynamic_form input[name="method"]').val(type);
			}).on('click', '.intelligent_box .scope_box .type_item', function () {
				let _this = $(this),
					_value = _this.find('input[name="scope"]').val();
				$Obj.find('.intelligent_box .scope_box .item').removeClass('checked').find('input').prop('checked', false);
				_this.addClass('checked').find('input').prop('checked', true);
				if (_value == 'specify_category') {
					$Obj.find('.intelligent_box .scope_box .category_box').show();
				} else {
					$Obj.find('.intelligent_box .scope_box .category_box').hide();
				}
			})

			$Obj.find('.menu_type .menu_box .search_form .more').on('click', function () {
				module_filter_init();
			});
			$Obj.on('click', function (e) {
				if (!$(e.target).hasClass('more') && !$(e.target).hasClass('ext') && !$(e.target).parent().hasClass('ext') && !$(e.target).parents('.ext').length && !$(e.target).hasClass('search_btn') && $Obj.find('.menu_type .menu_box .search_form .ext').css('display') == 'block') {
					$Obj.find('.menu_type .menu_box .search_form .more').click()
				}
			})
			$Obj.on('click', function (e) {
				if (!$(e.target).hasClass('more') && !$(e.target).hasClass('ext') && !$(e.target).parent().hasClass('ext') && !$(e.target).parents('.ext').length && !$(e.target).hasClass('search_btn') && $Obj.find('.middle_top_menu .list_menu .search_form .ext').css('display') == 'block') {
					$Obj.find('.menu_type .menu_box .search_form .more').click()
				}
			})
			frame_obj.check_amount($Obj);

			frame_obj.submit_form_init($('#dynamicBox .dynamic_form'), '', '', '', function (result) {
				if (result.ret == 1) {
					let Data = result.msg.Data,
						inputObj = $(`input[name="${result.msg.Position}"]`);
					inputObj.val(Data);
					$Obj.remove();
					save_visual_form(
						inputObj,
						() => data_init.apply(inputObj)
					);
				} else {
					global_obj.win_alert_auto_close(result.msg, 'fail', '1000', '20%')
				}
			})

			$vIdStr = $Obj.find('.dynamic_search_form input[name="vIdStr"]').val();
			if ($vIdStr) {
				$vIdObject = $vIdStr.split(",");
				if ($vIdObject) dynamicList = $vIdObject;
			}

			$Obj.find('.menu_type .menu_box .item.checked').trigger('click');

		}
		// 请求数据
		var dynamic_select_choice = (url = '') => {

			$Obj = $('#dynamicBox');
			let searchObj = $('.dynamic_search_form'),
				type = $('#dynamicBox').find('.box_type_menu input[name="method"]:checked').val();
			let filterObj = $Obj.find('.menu_type .menu_box .search_form');
			let tagId = [];// 标签
			filterObj.find("input[name*='_tagsCurrent[]']").each(function (index, element) {
				tagId[index] = $(element).val();
			});
			let moreId = [];
			filterObj.find("input[name='MoreCurrent[]']").each(function (index, element) {
				moreId[index] = $(element).val();
			});
			cateId = filterObj.find('input.hidden_value[name="CateId"]').val();
			tagId = tagId.join(",");
			moreId = moreId.join(",");
			$Obj.find('.drop_down').hide();;
			filterObj.find('.drop_down').siblings('input[name=tagId]').val(tagId);
			filterObj.find('.drop_down').siblings('input[name=moreId]').val(moreId);
			if (!url) url = '/manage/view/visual-v2/get-dynamic-choice';
			if (type == 'manual') {
				$.post(url, searchObj.serialize() + '&dynamicList=' + JSON.stringify(dynamicList), function (result) {
					if (result.ret == 1) {
						$('#dynamicBox').find('.data_box').html(result.msg.Html);
						$('#dynamicBox').find('.page_box').html(result.msg.Page);
						$('#dynamicBox').find('.data_box').attr('data-page', result.msg.getPage).attr('per-page-total', result.msg.perPageTotal);
						filterMenuAry = result.msg.filterAry;
					}
					dynaminc_total_set_init();
					dynamic_search_filter_current_init();
				}, 'json');
			}

		}
		var dynamic_search_filter_current_init = () => {
			let $Obj = $('#dynamicBox');
			let searchBox = $Obj.find('.search_form');
			let cateId = searchBox.find('input[name=cateId]').val();
			let tagId = searchBox.find('input[name=tagId]').val();
			let moreId = searchBox.find('input[name=moreId]').val();
			// 着色
			let html = '';
			if (cateId > 0 && filterMenuAry.cateid[cateId]) {
				html += '<span class="btn_item_choice current" data-name="cateId"><b>' + lang_obj.manage.sales.coupon.products_category + ': ' + filterMenuAry.cateid[cateId] + '</b><i></i></span>';
			}
			tagId = tagId.split(',').filter(item => item != '');
			if (tagId.length) {
				html += '<span class="btn_item_choice current" data-name="tagId"><b>' + lang_obj.manage.sales.coupon.tags + ': ';
				for (let key in tagId) {
					if (key > 0) html += ', ';
					html += filterMenuAry.tagid[tagId[key]];
				}
				html += '</b><i></i></span>';
			}
			moreId = moreId.split(',').filter(item => item != '');
			if (moreId.length) {
				html += '<span class="btn_item_choice current" data-name="moreId"><b>' + lang_obj.manage.global.more + ': ';
				for (let key in moreId) {
					if (key > 0) html += ', ';
					html += filterMenuAry.more[moreId[key]];
				}
				html += '</b><i></i></span>';
			}
			$Obj.find('.middle_top_menu .search_box_selected').html(html);
			//删除筛选选项
			if ($("#dynamicBox .search_box_selected").length) {
				$("#dynamicBox .search_box_selected .btn_item_choice").on("click", function () {
					let $name = $(this).attr("data-name");
					nameAry = $name.split("|");
					if ($name == 'tagId') {
						$('#dynamicBox .search_form').find('.i_select_tags').find('.btn_attr_choice').remove();
					}
					if ($name == 'moreId') {
						$('#dynamicBox .search_form').find('.i_select_more').find('.btn_attr_choice').remove();
					}
					$(nameAry).each(function (index, element) {
						$(`#dynamicBox .search_form input[name="${element}"]`).val("");
					});
					$("#dynamicBox .search_form input[type=submit]").trigger("click");
					$(this).remove();
					if (!$("#dynamicBox .search_box_selected .btn_item_choice").length) {
						$("#dynamicBox .search_box_selected").html('');
					}
				});
			}
		}
		// 筛选请求数据
		var module_filter_init = () => {
			$Obj = $('#dynamicBox');
			let filterObj = $Obj.find('.menu_type .menu_box .search_form');
			let tagId = [];// 标签
			filterObj.find("input[name*='_tagsCurrent[]']").each(function (index, element) {
				tagId[index] = $(element).val();
			});
			let moreId = [];
			filterObj.find("input[name='MoreCurrent[]']").each(function (index, element) {
				moreId[index] = $(element).val();
			});
			tagId = tagId.join(",");
			moreId = moreId.join(",");
			filterObj.find('input[name=tagId]').val(tagId);
			filterObj.find('input[name=moreId]').val(moreId);

			let p_cateId = filterObj.find('input[name=cateId]').val();
			let p_tagId = filterObj.find('input[name=tagId]').val();
			let p_moreId = filterObj.find('input[name=moreId]').val();
			let p_moduleType = filterObj.find('input[name=moduleType]').val();
			$.post('/manage/view/visual-v2/select-module-filter', { 'CateId': p_cateId, 'TagId': p_tagId, 'MoreId': p_moreId, moduleType: p_moduleType }, function (data) {
				if (data.ret == 1) {
					$Obj.find('.menu_type .menu_box .search_form .ext').html(data.msg);
					if ($('.menu_type .menu_box .search_form .ext').is(':hidden')) {
						$('.menu_type .menu_box .search_form .ext').show();
						$('.menu_type .menu_box .search_form form').css('border-radius', '5px 5px 0 0');
						$('.menu_type .menu_box .search_form .more').addClass('more_up');
					} else {
						$('.menu_type .menu_box .search_form .ext').hide();
						$('.menu_type .menu_box .search_form form').css('border-radius', '5px');
						$('.menu_type .menu_box .search_form .more').removeClass('more_up');
					}
				}
			}, 'json')
		}

		var dynaminc_total_set_init = () => {
			$('#dynamicBox .menu_box .select_count').text(dynamicList.length);
			var innerTotalPage = parseInt($('#dynamicBox .data_box').attr('per-page-total'));
			count = 0;
			$('#dynamicBox .data_box .item').each(function (k, v) {
				v = $(v)
				if (v.hasClass('checked')) {
					count++;
				}
			})
			if (count == innerTotalPage) {
				$('#dynamicBox .menu_all_checkbox').addClass('checked').prop('checked', true);
			} else {
				$('#dynamicBox .menu_all_checkbox').removeClass('checked').prop('checked', false);
			}
			$('#dynamicBox').find('input[name="idStr"]').val(dynamicList);
		}

		// Seo组件初始化
		var componentSeoInit = () => {
			$Obj = $('#plugins_visual .tool_bar .tool_bar_fixed .fixed_global .tool_bar_bg');
			getStatus = true;
			$Obj.on('click', '.component_seo .seo_set_btn', function () {
				_this = $(this).parents('.component_seo'),
					_thisName = _this.find('input').attr('name'),
					_thisValue = _this.find('input').val(),
					_thisParent = _this.parents('.fixed_global'),
					PId = _thisParent.attr('data-plugins'),
					data = {
						'PId': PId,
						'Name': _thisName,
						'Value': _thisValue
					}
				if (!getStatus) return false;
				getStatus = false;
				$.post('/manage/view/visual-v2/get-visual-seo-html', data, function (result) {
					getStatus = true;
					if (result.ret == 1) {
						global_obj.div_mask();
						$('#plugins_visual').after(result.msg.Html);
						$('#visual_seo_box').addClass('show');
						$('#visual_seo_box .input_time').daterangepicker({ singleDatePicker: true })
						frame_obj.submit_form_init($('#visual_seo_box .visual_seo_form'), '', '', '', function (result) {
							let msg = result.msg,
								ret = result.ret;
							inputObj = $(`input[name="${msg.Position}"]`);
							if (ret == 1 && inputObj.length > 0) {
								inputObj.val(msg.Data);
								inputObj.parent().find('.seo_box').find('.cont').remove();
								if (msg.Html) {
									inputObj.parent().find('.seo_box').find('.seo_no_data').hide().after('<div class="cont">' + msg.Html + '</div>');
								} else {
									inputObj.parent().find('.seo_box').find('.seo_no_data').show();
								}
								save_visual_form(
									inputObj,
									() => data_init.apply(inputObj)
								);
							}
							$('#visual_seo_box').remove();
							global_obj.div_mask(1);
						})

						$('#visual_seo_box').on('click', '.close_btn , .btn_cancel', function () {
							global_obj.div_mask(1);
							$('#visual_seo_box').remove();
						})
					}
				}, 'json')

			})
		}

		// 异步后需要重载的函数
		var reload_init = () => {
			// 颜色插件初始化
			component_color_init();
			// 图片上传初始化
			frame_obj.upload_img_init();
			//本地视频上传初始化
			frame_obj.upload_file_init();
			// 组件联动初始化
			component_linkage_init();
			// 富文本插件初始化
			component_richtext_init();
			// 拖动排序初始化
			dragsort_init();
			// 初始化主图
			banner_init();
			// 初始化导航图片与标题
			menuBlocksPicTitleInit();
			// 时间组件初始化
			component_time_init();
			//优惠券组件初始化
			coupon_select_init();
			// 动态插件面板初始化
			dynamic_select_init();
			// Seo插件面板初始化
			componentSeoInit();
			// 检查插件状态
			check_plugins_status();
		}
		// 保存数据
		var save_visual_form = (obj = null, callback) => {
			setProgressRate();
			publishStatus = false;
			if ((window.CKEDITOR_VERSION || false) && (typeof window.CKEDITOR == 'object')) {
				Object.values(window.CKEDITOR.instances).forEach(_ => _.updateSourceElement())
			} else if (typeof CKEDITOR == 'object') {
				for (let i in CKEDITOR.instances) CKEDITOR.instances[i].updateElement();  // 更新编辑器内容
			}

			let params = new Array(),
				num = 0,
				input = 'input,select,textarea';
			if (obj) {
				parentsObj = obj.parents('.fixed_global');
				if (obj.hasClass('fixed_global')) parentsObj = obj;
				if (parentsObj.length >= 1) {	//如果不存在的话 那就证明的是点击复制、隐藏等操作
				

					// 获取对应的主插件Id
					Plugins = parseInt(parentsObj.data('plugins'));
					if (parentsObj.attr('data-plugins').indexOf("global-set") !== -1) {
						parentPluginsObj = $('[data-plugins*="global-set"]');
					} else {
						// 获取右侧主页面的插件数据
						parentPluginsObj = $('.fixed_global[data-plugins="' + Plugins + '"]');
					}
					if (Plugins && typeof (Plugins) == 'number') {
						params[num] = { name: 'EditPId', value: Plugins };
						num++;
					}
					parentPluginsObj.find(input).each(function (k, v) { //setting
						//debugger;
						var v = $(v)
						n = v.attr('name');
						val = v.val()
						if (v.hasClass('hidden_type') && n.indexOf(']Type') >= 0) return true;
						params[num] = { name: `${n}`, value: `${val}` };
						num++;
					})
					HasBlocks = parentPluginsObj.find('.plugins_blocks .blocks_item .menu_item').length > 0 ? true : false
					if (HasBlocks) {
						fixedBlocks = parentPluginsObj.find('.plugins_blocks .blocks_item .menu_item');
						fixedBlocks.each(function (key, val) { //blocks
							val = $(val);
							fixed_blocks_plugins = val.data('fixed-plugins');
							$('.fixed_global[data-plugins="' + fixed_blocks_plugins + '"]').find(input).each(function (k, v) {
							 //block
								var v = $(v)
								n = v.attr('name');
								vv = v.val()
								if (v.hasClass('hidden_type') && n.indexOf(']Type') >= 0) return true;
								params[num] = { name: `${n}`, value: `${vv}` };
								num++;
							})
						})
					}
				}
			}
	
			$('#form_visual>input[type="hidden"]').each(function (k, v) {  //主参数  page/draft
				//debugger
				var v = $(v)
				n = v.attr('name');
				val = v.val()
				params[num] = { name: `${n}`, value: `${val}` };
				num++;
			})
			$('#plugins_visual .tool_bar .tool_bar_menu').find(input).each(function (k, v) {//pid[]
				//debugger
				var v = $(v)
				n = v.attr('name');
				val = v.val()
				params[num] = { name: `${n}`, value: `${val}` };
				num++;
			})
			formData = params;

			setTimeout(() => {
				$.ajax({
					url: '/manage/view/visual-v2/save/',
					type: 'POST',
					dataType: 'json',
					data: formData,
					success: (data) => {
						publishStatus = true;
						if (data.ret == 1) {
							setProgressRate(1);
							if ($.isFunction(callback)) callback.call();
							// 改动过使用中状态变为草稿状态
							if ($('#visual .go_status').hasClass('using')) {
								$('#visual .go_status').removeClass('using').text(lang_obj.manage.module.draft);
								global_obj.win_alert_auto_close(lang_obj.manage.module.drafts_success, '', 2000, '8%');
							}

							$('#plugins_visual').find('input[name="defaultLanguage"]').attr('dis-allow-pic-sync-list', data.msg.disAllowPicSyncList);
						} else if (data.ret == -1) {
							let params = {
								'confirmBtn': lang_obj.manage.view.reload,
								'confirmBtnClass': 'btn_warn'
							};
							let _tipsTitle = lang_obj.manage.view.pages_change;
							params.title = _tipsTitle.replace('{name}', data.msg.name)
							global_obj.win_alert(params, function () {
								window.location.reload();
							});
						} else {
							global_obj.win_alert_auto_close(data.msg, 'await', 2000, '8%');
						}
					}
				});
			}, 700);
		}
		// 数据初始化
		var data_init = function () {
			setProgressRate();
			if ($(this).length > 0) {
				let pidAry = new Array(),
					jumpUrl = iframeUrl;
				if ($(this).length > 1) {
					for ($i = 0; $i < $(this).length; $i++) {
						obj = $(this)[$i];
						plugins = obj.data('plugins') ? obj.data('plugins').toString() : obj.parents('*[data-plugins]').data('plugins').toString();
						pidAry[$i] = parseInt(plugins.split('-')[0]);
					}
				} else {
					obj = $(this);
					plugins = obj.data('plugins') ? obj.data('plugins').toString() : obj.parents('*[data-plugins]').data('plugins').toString();
					pidAry[0] = parseInt(plugins.split('-')[0]);
				}
				if (isNaN(pidAry)) {
					pidAry = new Array();
				}
				if (pidAry && pidAry.length > 0) {
					jumpUrl = getClientUrl(jumpUrl);
					$.post(`${jumpUrl}`, { Id: pidAry, do_action: 'action.visualPluginsInit' }, (data) => {
						if (data.ret == 1) {
							// 推送自定义事件
							let customEvent = new CustomEvent('visualChangeHtml', {
								detail: {
									pid: pidAry,
									html: data.msg.html,
									font: data.msg.font
								}
							});
							$('#plugins_iframe_themes')[0].contentWindow.dispatchEvent(customEvent);
							// 更新导航状态
							if (pidAry.length) {
								for ($i = 0; $i < pidAry.length; $i++) {
									menuBlocksPicTitleInit(pidAry[$i]);
								}
							} else {
								menuBlocksPicTitleInit();
							}
							setProgressRate(1);
						}
					}, 'json');
				} else {
					document.getElementById('plugins_iframe_themes').contentWindow.location.reload();
				}
			} else {
				setProgressRate(1);
			}
		}
		// 主图初始化
		var banner_init = function () {
			// 点击切换图片
			$("#plugins_visual .tool_bar .fixed_global .plugins_blocks .menu_item[data-fixed-plugins*='-Banner-']").off('click').on('click', function () {
				let plugins = $(this).data('fixed-plugins');
				let pluginsObj = $(`#plugins_visual .tool_bar .fixed_global[data-plugins='${plugins}']`);
				data_init.apply(pluginsObj);
			})
			// 退出弹窗取消当前编辑模式
			$("#plugins_visual .tool_bar .fixed_global[data-plugins*='-Banner-'] .goback").on('click', () => {
				$('#plugins_iframe_themes').contents().find('#banner_edit').removeClass('current_edit');
			});
			// 图片上传自动补全移动端
			$("#plugins_visual .tool_bar .fixed_global[data-plugins*='-Banner-'] input[type=hidden][name*='[PicPc]']").off('change').on('change', function () {
				let picPic = $(this).val();
				let mobileObj = $(this).parents('.plugins_settings').find("input[type=hidden][name*='[PicMobile]']");
				let picMobile = mobileObj.val();
				let mobileWidth = mobileObj.parents('.component_image').data('width');
				let mobileHeight = mobileObj.parents('.component_image').data('height');
				if (!picPic || picMobile) return;
				$.post('/manage/view/visual-v2/add-mobile-pic', { 'Pic': picPic, 'Tips': `${mobileWidth}*${mobileHeight}` }, (result) => {
					if (result.ret == 1) {
						mobileObj.parents('.component_image').find('.img').addClass('isfile').find('input[type=hidden]').val(result.msg).prev().hide().parent().append('<a href="javascript:;"><img src="' + result.msg + '" /><em></em></a>').parents('.image').find('.zoom').attr('href', result.msg);
						save_visual_form(
							$(this),
							() => data_init.apply(this)
						);
					}
				}, 'json');
			});

		}
		// 导航和内容块标题与图片替换
		var menuBlocksPicTitleInit = (pid = 0) => {
			// 过滤HTML标签并截取长度
			let stripTagsLength = (text) => {
				text = text.replace(/<.*?>/g, '');
				text = text.substr(0, 100);
				return text;
			}
			// 匹配标题与图片
			let findData = (pluginsObj, params, plugins) => {
				// 查找图片
				let titleAry = ['Title', 'SubTitle', 'Content'];
				for (let key of titleAry) {
					if (params.title) break;
					let titleInput = pluginsObj.find(`.plugins_settings>div[class*=component_] *[name*='[${key}]']`);
					if (!titleInput.length) continue;
					let titleInputValue = stripTagsLength(titleInput.val());
					if (!titleInputValue) continue;
					params.title = titleInputValue;
					if (typeof (window.VisualReplaceData[plugins].title) == 'undefined' || window.VisualReplaceData[plugins].title == '') {
						window.VisualReplaceData[plugins].title = titleInputValue;
					}
				}
				// 查找图片
				let imageInput = pluginsObj.find(`.plugins_settings .component_image .image_box input[name*='visual[']`);
				imageInput.each(function () {
					let imageInputValue = $(this).val();
					if (!params.image) {
						params.image = imageInputValue;
						console.log("window.VisualReplaceData[plugins]:");
						console.log(window.VisualReplaceData[plugins]);
						if (typeof (window.VisualReplaceData[plugins]) != 'undefined') {
							window.VisualReplaceData[plugins].image = imageInputValue;
						}
						
					}
				});
			}
			// 替换图片与标题
			let replaceData = function (params) {
				let name = params.title ? params.title : $(this).find('.item_name').data('name');
				$(this).find('.item_name').text(name);
				params.image ? $(this).find('.item_icon').css({ 'background-image': `url(${params.image})`, 'background-size': 'cover', 'background-position': 'center' }) : $(this).find('.item_icon').removeAttr('style');
			}
			let menuObj = pid > 0 ? $(`#plugins_visual .tool_bar .tool_bar_menu .menu_item[data-fixed-plugins=${pid}]`) : $('#plugins_visual .tool_bar .tool_bar_menu .menu_item');
			// 循环导航卡
			menuObj.each(function () {
				let icon = $(this).find('.item_icon');
				if (icon.hasClass('icon_header') || icon.hasClass('icon_footer')) return true;
				// 初始化图片与标题
				let menuParams = { 'title': '', 'image': '' };
				// 查找对应弹窗
				let plugins = $(this).data('fixed-plugins');
				let pluginsObj = $(`#plugins_visual .tool_bar .fixed_global[data-plugins='${plugins}']`);
				// 查找settings是否存在匹配
				if (pluginsObj.find('.plugins_settings').length) findData(pluginsObj, menuParams, plugins);
				// 查找blocks是否存在匹配
				if (pluginsObj.find('.plugins_blocks').length) {
					pluginsObj.find('.plugins_blocks .menu_item').each(function () {
						let blocksParams = { 'title': '', 'image': '' };
						let blocksPlugins = $(this).data('fixed-plugins');
						let blocksPluginsObj = $(`#plugins_visual .tool_bar .fixed_global[data-plugins='${blocksPlugins}']`);
						findData(blocksPluginsObj, blocksParams, plugins);
						// 替换图片与标题
						replaceData.call(this, blocksParams);
						if (!menuParams.title && blocksParams.title) menuParams.title = blocksParams.title;
						if (!menuParams.image && blocksParams.image) menuParams.image = blocksParams.image;
					});
				}
				if (typeof (window.VisualReplaceData) == 'object' && typeof (window.VisualReplaceData[plugins]) == 'object') {
					if (typeof (window.VisualReplaceData[plugins].title) != 'undefined' && window.VisualReplaceData[plugins].title) menuParams.title = window.VisualReplaceData[plugins].title;
					if (typeof (window.VisualReplaceData[plugins].image) != 'undefined' && window.VisualReplaceData[plugins].image) menuParams.image = window.VisualReplaceData[plugins].image;
				}
				// 替换图片与标题
				replaceData.call(this, menuParams);

			});
		}
		// 设置加载滚动条
		var setProgressRate = (rate = 0) => {
			let scheduleBar = $('#plugins_visual .schedule_bar');
			if (rate == 0 && !scheduleBar.length) {  // 加载中
				$('#plugins_visual').append('<div class="schedule_bar"></div>');
			} else if (rate == 1 && scheduleBar.length) {  // 加载完成
				setTimeout(() => {
					scheduleBar.addClass('success').fadeOut(function () {
						$(this).remove();
					});
				}, 500);
			}
		}
		// 自动填充内容
		var autofillInit = function (callback) {
			let componentObj = $(this).parents("*[class*='component_'][data-component]");
			let componentType = componentObj.data('component');
			let autofillType = componentObj.data('autofill-type');
			let autofill = componentObj.data('autofill');
			let fixed = $(this).parents('.fixed_global');
			let id = 0;
			if (componentType == 'category') {
				id = componentObj.find('input[type=hidden]').val();
			}
			if (!autofill || !id) {
				if ($.isFunction(callback)) callback.call();
				return false;
			}
			$.post('/manage/view/visual-v2/autofill/', { type: autofillType, id: id }, (data) => {
				if (data.ret == 1) {
					for (let key in autofill) {
						let inputObj = fixed.find(`input[name*='[${autofill[key]}]']`);
						let ComponentObj = inputObj.parents("*[class*='component_'][data-component]");
						let autofillComponent = ComponentObj.data('component');
						if (autofillComponent == 'input') {  // 单行文本组件
							let inputValue = inputObj.val();
							if (!inputValue && data.msg[key]) inputObj.val(data.msg[key]);
						} else if (autofillComponent == 'image') {  // 图片上传组件
							let inputValue = inputObj.val();
							if (!inputValue && data.msg[key]) {
								ComponentObj.find('.img').addClass('isfile').find('input[type=hidden]').val(data.msg[key]).prev().hide().parent().append('<a href="javascript:;"><img src="' + data.msg[key] + '" /><em></em></a>').parents('.image').find('.zoom').attr('href', data.msg[key]);
							}
						} else if (autofillComponent == 'link') {  // 链接组件
							if (!inputObj.eq(1).val() && data.msg[key]) inputObj.eq(1).val(data.msg[key]);
						}
					}
					if ($.isFunction(callback)) callback.call();
				}
			}, 'json');
		}
		// 替换带端口地址
		var getClientUrl = (url) => {
			if (url.indexOf('?') == -1) {
				url += `?client=${client}`;
			} else {
				if (url.indexOf('client=') == -1) {
					url += `&client=${client}`;
				} else {
					url = url.indexOf('client=website') == -1 ? url.replace(/client=mobile/g, `client=${client}`) : url.replace(/client=website/g, `client=${client}`);
				}
			}
			return url;
		}
		//时间组件初始化
		var component_time_init = () => {
			$(".input_time").daterangepicker({
				showDropdowns: true,
				singleDatePicker: true
			});
		}
		//时间日期转时间戳
		var dataToTime = (data) => {
			let _data = new Date(data);
			let _time = _data.getTime();
			return _time;
		}
		// 关联产品
		var relatedProducts = (draftsId, pagesId, proInfo = {}) => {
			let params = { iframeTitle: lang_obj.manage.view.select_products, type: 'manual', isOrder: true, value: {}, valueOrder: [] };
			let proInfoLength = Object.keys(proInfo).length;
			if (proInfoLength) {
				for (let key in proInfo) {
					params.value[proInfo[key].proid] = { image: proInfo[key].image };
					params.valueOrder.push(proInfo[key].proid);
				}
			}
			frame_obj.products_choice_iframe_init_v2({
				params: params,
				onSelect: (data) => {
					onSelectFunc(data, 'related');
				},
				onSubmit: (data) => {
					let proid = [];
					for (let key in data.value) {
						proid.push(data.value[key].proid);
					}
					if (JSON.stringify(params.valueOrder) === JSON.stringify(proid)) return false;
					$.post('/manage/view/visual-v2/product-related', { DraftsId: draftsId, PagesId: pagesId, idAry: proid }, (result) => {
						if (result.ret == 1) {
							deleteSameId(result.msg.delPagesId);
							$(`#plugins_visual .top_bar .go_select .page_select_box .sub_box .select_item[data-pages-id='${pagesId}']`).parent('.menu_item').replaceWith(result.msg.menuItem);
							$(`#plugins_visual .top_bar .go_select .page_select_box .sub_box .select_item[data-pages-id='${pagesId}']`).trigger('click');
						}
					}, 'json');
				}
			});
		}
		//关联单页
		var relatedFiexdPages = (draftsId, pagesId, inputValue, page) => {
			custom_page_select_init(inputValue, page, '', 0, 1, function (data) {
				$.post('/manage/view/visual-v2/product-related', { DraftsId: draftsId, PagesId: pagesId, idAry: data }, (result) => {
					if (result.ret == 1) {
						deleteSameId(result.msg.delPagesId, page);
						$(`#plugins_visual .top_bar .go_select .page_select_box .sub_box .select_item[data-pages-id='${pagesId}']`).parent('.menu_item').replaceWith(result.msg.menuItem);
						$(`#plugins_visual .top_bar .go_select .page_select_box .sub_box .select_item[data-pages-id='${pagesId}']`).trigger('click');
					}
				}, 'json');
			}, function (data) {
				let _idData = { value: {} };
				for (k in data) {
					_idData['value'][k] = { proid: data[k] }
				}
				onSelectFunc(_idData, 'related', page);
			})
			let _btnId = 'add_article';
			if (page == 'list') _btnId = 'add_product_category';
			fiexd_global($('#' + _btnId));
		}
		var calculateRatio = (data, object = '') => {
			$.post('/manage/view/visual-v2/calculate-image-ratio', data, function (result) {
				plugins = data.pluginsId;
				PluginObj = $(`#plugins_visual .tool_bar .fixed_global[data-plugins='${plugins}']`)
				if (result.ret == 1) {
					$Value = result.msg.PicHeight;
					$inputObj = result.msg.inputObj;
					obj = $($inputObj).parents('.progress_bar');
					if (obj) {
						$Max = obj.attr('data-max');
						$Min = obj.attr('data-min');
						$objWidth = obj.width();
						$width = Math.ceil((($Value - $Min) / ($Max - $Min)) * $objWidth);
						//$processObj = obj.find('input[type=hidden]').val(result.msg.PicHeight+'px');
						progressScoll(obj, $width);
					}
				}
				if (typeof ($inputObj) == 'undefined' && object) $inputObj = object;

				save_visual_form(
					$($inputObj),
					() => data_init.apply(PluginObj)
				);;
			}, 'json')

		}

		var progressScoll = function (obj, left) {
			let _this = obj;
			let objLeft = obj.offset().left; // 组件的左位移
			let objWidth = obj.width();  // 组件的宽度
			let min = obj.data('min');  // 组件值的最小值
			let max = obj.data('max');  // 组件值的最大值
			let suffix = obj.data('suffix');  // 组件值的后缀
			let per = parseFloat(objWidth) / (parseFloat(max) - parseFloat(min));  // 组件每1值代表的像素

			// 校对最大最小值
			if (left < 0) left = 0;
			if (left > parseFloat(objWidth)) left = parseFloat(objWidth);
			// 计算当前的偏移值
			let value = parseFloat(left) / parseFloat(per);
			// 消除每1值代表的像素差距过大导致用户体验感不好
			if (per > 10) value = Math.round(value);
			// 防止计算误差除不尽导致选不到最小或最大值
			if (left == 0) value = Math.floor(value);
			if (left == objWidth) value = Math.ceil(value);
			// 计算当前的值
			value = parseInt(value) + parseInt(min);
			// 计算当前的值占据总进度的百分比
			let percentage = (parseFloat(value) - parseFloat(min)) / (parseFloat(max) - parseFloat(min)) * 100;
			obj.find('.progress_belt').width(`${percentage}%`);
			// 赋值
			obj.find('input[type=hidden]').val(value + suffix);
			obj.parents('.progress_box').siblings('.progress_name').find('span').text(value + suffix);
		}

		// 产品详细模板, 产品弹窗选中后校对产品是否已经被关联
		var onSelectFunc = (data, type, pages = 'goods') => {
			let key = Object.keys(data.value).length - 1;
			let proid = data.value[key].proid;
			let currentPagesId = $('#plugins_visual input[name=PagesId]').val();
			let menu = $(`#plugins_visual .top_bar .go_select .page_select_box .sub_box[data-id="${pages}"] .menu_item`);
			menu.each(function () {
				let id = $(this).find('.select_item').data('id');
				let pagesId = $(this).find('.select_item').data('pages-id');
				let idPlus = $(this).find('.select_item').attr('id-plus');
				let exp = [];
				if (idPlus) exp = idPlus.split(',').filter(item => item != '').map(item => parseInt(item));
				if ((id > 0 && id == proid) || (idPlus && $.inArray(proid, exp) != -1)) {
					if (type == 'related' && currentPagesId == pagesId) return true;
					let _tips = lang_obj.manage.view.related_tips;
					if (pages == 'article') _tips = lang_obj.manage.view.article_related_tips;
					if (pages == 'list') _tips = lang_obj.manage.view.list_related_tips;
					global_obj.win_alert_auto_close(_tips, 'await', 2000, '8%', 0);
					return false;
				}
			});
		}

		// 产品详细模板删除关联产品
		var deleteSameId = (delPagesId, pages = 'goods') => {
			let main = delPagesId.main;
			let sub = delPagesId.sub;
			if (main.length) {  // 主id
				for (let key in main) {
					$(`#plugins_visual .top_bar .go_select .page_select_box .sub_box .select_item[data-pages-id='${main[key]}']`).parent('.menu_item').remove();
				}
			}
			if (sub.length) {  // 多个id
				$(`#plugins_visual .top_bar .go_select .page_select_box .sub_box[data-id=${pages}] .select_item`).each(function () {
					let idPlus = $(this).attr('id-plus');
					if (idPlus) {
						for (let key in sub) {
							idPlus = idPlus.replace(`,${sub[key]},`, ',');
						}
						$(this).attr('id-plus', idPlus);
					}
				});
			}
		}

		//检查高度之后处理的逻辑
		var checkHeightStutas = () => {
			let _height = $(window).height();
			if (_height < 900) {
				if ($('#plugins_visual .tool_bar .related_products .down_icon').length) {
					$('#plugins_visual .tool_bar .related_products .down_icon').trigger('click');
				}
			}
		}

		// 初始化变量
		var _init = false;
		var client = $('#plugins_visual .top_bar .go_screen i.current').data('client');
		var iframeUrl = $('#plugins_iframe_themes').attr('src');
		var publishStatus = true;

		// 加入草稿箱弹窗提示
		let drafts = global_obj.query_get('drafts');
		if (drafts) {
			global_obj.win_alert_auto_close(lang_obj.manage.module.drafts_success, '', 2000, '8%');
			let Url = window.location.href.replace('&drafts=1', '');
			let State = { title: '', url: Url };
			window.history.replaceState(State, '', Url);
		}

		// 初始化函数
		global_init();
		menu_init();
		plugins_init();
		component_init();
		switch_mode_init();
		view_obj.switch_style_init();
	},

	visual_list_init: function () {
		//草稿箱点击编辑
		$('#visual .container .btn_box .visual_edit_btn').on('click', function () {
			if ($(this).attr('disabled')) {
				return false;
			}
			$(this).attr('disabled', 'disabled');
			let DraftsId = $(this).attr('data'),
				Data = { "DraftsId": DraftsId, "returnType": 1 };
			if (DraftsId) {
				$.post('/manage/view/visual/change-tmp-drafts', Data, function (result) {
					if (result.ret == 1) {
						$(this).removeAttr('disabled');
						DraftsId = result.msg;
						window.location.href = '/Store/VisualEdit?DraftsId=' + DraftsId + '&publicFull=1';
					}
				}, 'json')
			}
			return false;
		})
		// 重命名草稿箱弹窗
		frame_obj.fixed_right($('.operation .rename'), '.fixed_drafts_rename', function ($this) {
			let DraftsId = $this.parents('tr').data('id');
			let Rename = $this.parents('tr').find('td.title em').text();
			$('#drafts_rename_form input[name=DraftsId]').val(DraftsId);
			$('#drafts_rename_form input[name=Rename]').val(Rename);
		});

		frame_obj.submit_form_init($('#drafts_rename_form'), '', '', '', function (data) {
			if (data.ret == 1) {
				window.location.reload();
			} else {
				global_obj.win_alert_auto_close(data.msg, 'await', 1000, '8%');
			}
		});

		// 删除草稿箱
		frame_obj.del_init($('.box_drafts .r_con_table .drop_down'));
		// 复制草稿箱，发布草稿箱
		$('.box_drafts .copy, .box_drafts .publish').on('click', function () {
			let Action = $(this).hasClass('publish') ? 'publish' : 'copy';
			let DraftsId = $(this).parents('tr').data('id');
			let Themes = $(this).parents('tr').find('.title strong').text();
			let Rename = $(this).parents('tr').find('.title em').text();
			if (Rename) Rename = '(' + Rename + ')';
			let Tips, Do, Success;
			let Parameter = { DraftsId: DraftsId };
			if (Action == 'publish') {  // 发布草稿箱
				Tips = lang_obj.manage.module.drafts_publish;
				Success = lang_obj.manage.module.drafts_publish_success;
				Do = '/manage/view/visual-v2/publish/';
			} else if (Action == 'copy') {  // 复制草稿箱
				Tips = lang_obj.manage.module.drafts_copy;
				Success = lang_obj.manage.module.drafts_copy_success;
				Do = '/manage/view/visual-v2/copy/';
				Parameter.IsRename = 1;
			}
			Tips += Themes + Rename;
			global_obj.win_alert(Tips, function () {
				$.post(Do, Parameter, function (data) {
					if (data.ret == 1) {
						global_obj.win_alert_auto_close(Success, '', 1000, '8%');
						window.location.reload();
					}
				}, 'json');
			}, 'confirm');
		});
		// 切换模板
		view_obj.switch_style_init();
		// 切换版本
		$('.go_to_old').on('click', function () {
			$.post('/manage/view/visual-v2/switch-version', (data) => {
				if (data.ret == 1) window.location.href = $(this).data('url');
			}, 'json');
		});
		// 关闭旧版
		$('.close_version_btn').on('click', () => {
			let params = {
				'title': lang_obj.manage.view.close_old_version,
				'confirmBtn': lang_obj.global.close,
				'confirmBtnClass': 'btn_warn'
			};
			global_obj.win_alert(params, () => {
				$.post('/manage/view/visual-v2/close-version', (data) => {
					if (data.ret == 1) window.location.reload();
				}, 'json');
			}, 'confirm');
		});

		frame_obj.select_all($('input[name=select_all]'), $('input[name=select]')) // 批量操作
		$('.table_menu_button .del').on('click', function () {
			let url = '/manage/view/visual-v2/batch-del-drafts',
				id = '',
				params = {
					'title': lang_obj.global.del_confirm,
					'confirmBtn': lang_obj.global.del,
					'confirmBtnClass': 'btn_warn'
				};
			$('input[name=select]:checked').each(function () {
				if ($(this).val() > 0) {
					id += (id ? '-' : '') + $(this).val();
				}
			})
			global_obj.win_alert(params, function () {
				$.post(url, { id: id }, function (data) {
					if (data.ret == 1) {
						window.location.reload();
					} else {
						global_obj.win_alert(data.msg);
					}
				}, 'json')
			}, 'confirm');
		})

	},

	switch_style_init: function () {
		$('.switch_style_btn').on('click', function () {  //切换风格触发按钮
			$('#template_list').removeClass('visual_edit').fadeIn();
			$('#template_list .terminal_list').show();
			if ($(this).hasClass('go_style')) {
				var $terminal = $('#plugins_visual .terminal_list a.cur').index();
				$('#template_list').addClass('visual_edit');
				$('#template_list .terminal_list a').eq($terminal).click();
				$('#template_list .terminal_list').hide();
			}
			$('#template_list .tem_box:visible').find('.rows').eq(0).find('img').each(function () {
				$(this).attr('src', $(this).attr('_src'));
			});
		});
		$('#template_list .close').on('click', function () {    //切换风格关闭按钮
			$('#template_list').fadeOut();
		});
		/*****风格选择开始*****/
		// 加入草稿箱
		$('.tem_box .list .edit_btn').on('click', function () {
			let $this = $(this);
			global_obj.win_alert(lang_obj.manage.module.sure_drafts, function () {
				$.get('/manage/view/visual-v2/add/', { themes: $this.attr('data-themes') }, function (data) {
					if (data.ret != 1) {
						global_obj.win_alert(data.msg, function () {
							window.location.reload();
						}, 'confirm');
					} else {
						window.location.href = '/manage/view/visual-v2/edit?DraftsId=' + data.msg + '&publicFull=1&drafts=1';
					}
				}, 'json');
			}, 'confirm');
		});

		let scrollFunc = function () {
			let $Box = $('#template_list'),
				$Top = $Box.scrollTop(),
				$Height = $Box.height(),
				$scrollHeight = $Box[0].scrollHeight;
			if (($Top + $Height + 300 >= $scrollHeight)) { //快下拉到底部的时候提前加载
				let $Show = $Box.find('.tem_box:visible .rows:hidden');
				if ($Show.length) {
					$Box.find('.tem_box:visible .loading').addClass('cur').find('span').text(lang_obj.manage.view.loading);
					$Show.eq(0).fadeIn();
					if ($Box.find('.tem_box:visible .rows:hidden').length) {
						$text = lang_obj.manage.global.load_more;
					} else {
						$Box.find('.tem_box:visible .loading').addClass('done');
						$text = lang_obj.manage.view.no_data;
					}
					$Box.find('.tem_box:visible .loading').removeClass('cur').find('span').text($text);
					$Show.eq(0).find('img').each(function () {
						$(this).attr('src', $(this).attr('_src'));
					});
				}
			}
		}
		$('#template_list').on('scroll', function () {
			scrollFunc();
		});

		$('#template_list').on('click', '.loading', function () {
			let $Box = $('#template_list'),
				$Show = $Box.find('.tem_box:visible .rows:hidden');
			if ($Show.length) {
				$Box.find('.tem_box:visible .loading').addClass('cur').find('span').text(lang_obj.manage.view.loading);
				$Show.eq(0).fadeIn();
				if ($Box.find('.tem_box:visible .rows:hidden').length) {
					$text = lang_obj.manage.global.load_more;
				} else {
					$text = lang_obj.manage.view.no_data;
					$Box.find('.tem_box:visible .loading').addClass('done');
				}
				$Box.find('.tem_box:visible .loading').removeClass('cur').find('span').text($text);
				$Show.eq(0).find('img').each(function () {
					$(this).attr('src', $(this).attr('_src'));
				});
			}
		})

		$('#template_list').scroll(function () { //记录滚动条位置
			var $Box = $('#template_list'),
				$Top = $Box.scrollTop();
			if ($Top) {
				$Box.addClass('small_head');
			} else {
				$Box.removeClass('small_head');
			}
			$Box.find('.tem_box:visible input:hidden').val($Top);
		});
		/*下拉加载结束*/
		/*****风格选择结束*****/
	},

	page_change_add_mode: function (DraftsId, Page = 'index') {
		var _this = $('.switch_page_style_btn');
		$('#page_template_list').find('.box').html('');
		if (_this.length) {
			var Params = { 'DraftsId': DraftsId, 'Page': Page };
			$.post('/manage/view/visual-v2/check-page-mode', Params, function (result) {
				if (result.ret == 1) {
					_this.show();
					$('#page_template_list').find('.box').html(result.msg);
				} else {
					_this.hide();
				}
			}, 'json')
		}
	},

	load_edit_form: function (target_obj, url, type, value, callback, fuc) {
		$.ajax({
			type: type,
			url: url + value,
			success: function (data) {
				if (fuc == 'append') {
					$(target_obj).append($(data).find(target_obj).html());
				} else {
					$(target_obj).html($(data).find(target_obj).html());
				}
				callback && callback(data);
			}
		});
	},

	nav_init: function () { //头部导航
		frame_obj.switchery_checkbox();  // 按钮开关
		frame_obj.del_init($('#nav .nav_item')); //删除提示
		$('#nav .nav_item .nav_ext').addClass('current');//默认隐藏
		$('#nav .nav_item .nav_ext').on('click', function () {  // 导航下拉
			var id = $(this).parent().data('id');
			if ($(this).parent().hasClass('second')) {
				id = id.substr(3);
			}
			$(this).toggleClass('current');
			$(this).parent().next('div[data-id=' + id + ']').slideToggle('fast');
		});
		frame_obj.fixed_right($('.btn_nav_edit'), '.nav_edit');  // 弹窗
		$('.btn_nav_edit').click(function () {  // 弹窗操作
			view_obj.load_edit_form('.nav_edit', $(this).attr('data-url'), 'get', '', function () {
				$('.box_drop_double').each(function () {
					frame_obj.box_drop_double_default($(this));
				});
				frame_obj.submit_form_init($('#nav_edit_form'));
				$('.box_drop_double input[name^="UnitValue"]').off('change').change(function () {
					var $value = $(this).val();
					$(this).parent().find('[name="Unit"]').val($value);
					$(this).parent().find('[name="UnitType"]').val('add');
					if ($(this).parent().find('[name="UnitType"]').val() == 'add') {
						$('#nav_edit_form').find('#custom_link').show();
						$('#nav_edit_form').find('#custom_link input[name=Link]').attr('notnull', 'notnull');
					}
				});
			});
		});
		$('body').on('click', '#nav_edit_form .input_radio_box', function () {
			let name = $(this).parent().find('.item_name').text();
			$('#nav_edit_form .rows input[name^="Name"]').val(name);
			if ($(this).parent().hasClass('drop_add')) {
				$('#nav_edit_form').find('#custom_link').show();
				$('#nav_edit_form').find('#custom_link input[name=Link]').attr('notnull', 'notnull');
			} else {
				$('#nav_edit_form').find('#custom_link').hide();
				$('#nav_edit_form').find('#custom_link input[name=Link]').val('');
			}
		});
		$('body').on('click', '#nav_edit_form .drop_list .item', function () {
			if ($(this).hasClass('children')) return false;
			$('#nav_edit_form .rows input[name^="Name"]').val($(this).data('name'));
			if (!$(this).hasClass('drop_add')) {
				$('#nav_edit_form').find('#custom_link').hide();
				$('#nav_edit_form').find('#custom_link input[name=Link]').val('').removeAttr('notnull');
			}
		});
		$('#nav .nav_main_box').dragsort({  // 元素拖动
			dragSelector: '.nav_dra_item',
			dragSelectorExclude: '.nav_ext, .nav_name, .nav_del, .nav_set, .nav_add_sub, .nav_second_box, .nav_third_box',
			placeHolderTemplate: '<div class="nav_dra_item placeHolder"></div>',
			scrollSpeed: 5,
			itemSelector: '.nav_dra_item',
			dragEnd: function () {
				var Type = $('#nav input[name=Type]').val();
				var do_action = $('#nav input[name=do_action]').val();
				var myorder_ary = {};
				$(this).parent().children('.nav_dra_item').each(function (index) {
					var MId = $(this).children('.nav_item').data('id');
					myorder_ary[MId] = index + 1;
				});
				$.post(do_action, { 'sort_order': myorder_ary, 'Type': Type });
			}
		});
		$('#nav .nav_main_box .nav_second_box').dragsort({  // 元素拖动
			dragSelector: '.nav_dra_second_item',
			dragSelectorExclude: '.nav_ext, .nav_name, .nav_del, .nav_set, .nav_third_box, .nav_add_sub',
			placeHolderTemplate: '<div class="nav_dra_second_item placeHolder"></div>',
			scrollSpeed: 5,
			itemSelector: '.nav_dra_second_item',
			dragEnd: function () {
				var Type = $('#nav input[name=Type]').val();
				var do_action = $('#nav input[name=do_action]').val();
				var myorder_ary = {};
				$(this).parent().children('.nav_dra_second_item').each(function (index) {
					var MId = $(this).children('.nav_item').data('id');
					MId = MId.substr(3);  // midxxx
					myorder_ary[MId] = index + 1;
				});
				$.post(do_action, { 'sort_order': myorder_ary, 'Type': Type });
			}
		});
		$('#nav .nav_main_box .nav_third_box').dragsort({  // 元素拖动
			dragSelector: '.nav_dra_third_item',
			dragSelectorExclude: '.nav_name, .nav_del, .nav_set',
			placeHolderTemplate: '<div class="nav_dra_third_item placeHolder"></div>',
			scrollSpeed: 5,
			itemSelector: '.nav_dra_third_item',
			dragEnd: function () {
				var Type = $('#nav input[name=Type]').val();
				var do_action = $('#nav input[name=do_action]').val();
				var myorder_ary = {};
				$(this).parent().children('.nav_dra_third_item').each(function (index) {
					var MId = $(this).children('.nav_item').data('id');
					MId = MId.substr(3);  // midxxx
					myorder_ary[MId] = index + 1;
				});
				$.post(do_action, { 'sort_order': myorder_ary, 'Type': Type });
			}
		});

		frame_obj.box_type_menu(function (obj) {
			let nav_type_val = obj.find('input[type=radio]').val();
			if (nav_type_val == 1) {
				obj.parent().next('.global_app_tips').fadeIn();
			} else {
				obj.parent().next('.global_app_tips').fadeOut();
			}
		});

		// 跳转到设置风格前需要查看是否已经开启了导航风格应用 ， 没有则自动开启
		$('#nav').on('click', '.set_nav_themes', function () {
			let $ClassName = $(this).data('type'),
				$IsInstall = $(this).data('install');
			if (!$IsInstall) {
				$.post('/manage/plugins/app/install', { 'ClassName': $ClassName }, function (data) {
					if (data.ret == 1) {
						window.location.href = data.msg
					}
					return false;
				}, 'json')
			} else {
				window.location.href = '/manage/plugins/nav-themes/index';
			}
		})


	},

	/* 页面 start */
	page_global: {
		del_action: '',
		order_action: '',
		init: function () {
			frame_obj.select_all($('input[name=select_all]'), $('input[name=select]'), $('.table_menu_button .del')); //批量操作
			frame_obj.del_bat($('.table_menu_button .del'), $('#page .r_con_table input[name=select]'), view_obj.page_global.del_action); //批量删除
		}
	},

	page_init: function () {
		view_obj.page_global.del_action = '/manage/view/page/delete';
		view_obj.page_global.init();
	},

	page_edit_init: function () {
		$('#edit_form').on('click', '.open', function () {
			if ($(this).hasClass('close')) {
				$(this).removeClass('close').text(lang_obj.global.open);
				$('.seo_hide').slideUp(300);
			} else {
				$(this).addClass('close').text(lang_obj.global.pack_up);
				$('.seo_hide').slideDown(300);
			}
		});
		if (typeof (CKEDITOR) == 'object') {
			for (i in shop_config.language) {
				var lang = shop_config.language[i],
					id = 'Content_' + lang;
				CKEDITOR.instances[id].on('change', function () {
					var $str = $(this)[0].getData();
					if ($str) {
						$str = $str.replace(/(<[^>]+>)|(&nbsp;)/g, '') //去掉html
							.replace(/(^\s*)|(\s*$)/g, '') //去掉前后空格
							.replace(/(\s+)|([\r\n])|([\r])|([\n])/g, ' ') //多个连续空格和换行替换成一个空格
							.slice(0, 254); //截取255个字符
					}
					if ($(this)[0].name && $('#' + id).data('change') == 1) {
						$('[name="SeoDescription_' + $(this)[0].name.replace('Content_', '') + '"]').val(global_obj.htmlspecialchars_decode($str));
					}
				});
			}
		}
		frame_obj.multi_lang_show_all('#edit_form');
		frame_obj.multi_lang_show_item('#edit_form');
		//修改 SEO 关键词
		frame_obj.fixed_right($('#edit_keyword'), '.fixed_edit_keyword', function ($this) {
			var $Id = $('input[name=AId').val();
			frame_obj.seo_edit_keyword({ 'do_action': '/manage/action/seo-keyword-select', 'Type': 'Article', 'field': 'AId', 'Id': $Id });
		});
		frame_obj.seo_keyword_form_submit();
		frame_obj.submit_form_init($('#edit_form'), '/manage/view/page/');

		// 预览
		$('.article_menu .btn_menu_view').click(function () {
			var $Url = $(this).parents('.article_menu').data('url');
			window.open($Url);
		});

		//SEO相关事件
		$('[name=PageUrl]').on('keyup', function (e) {
			var $Key = window.event ? e.keyCode : e.which,
				$Value = $.trim($(this).val());
			if ($Key == 8 && $Value == '') {
				//退格键 (不允许为空)
				$(this).val($('.left_container .rows:eq(0) .tab_txt:eq(0) input').val().replace(/\s+/g, '-'));
			}
		});
	},
	/* 页面 end */

	/******************* 绑定域名 *******************/
	domain_binding_init: function () {
		$('.del_domain_binding').click(function () {
			var $this = $(this),
				$domain = $this.attr('data-domain');
			let params = {
				'title': lang_obj.global.del_confirm,
				'confirmBtn': lang_obj.global.del,
				'confirmBtnClass': 'btn_warn'
			};
			global_obj.win_alert(params, function () {
				$.post('/manage/view/domain-binding/update/', { 'DomainBinding': $domain }, function (data) {
					if (data.ret == 1) {
						location.href = location.href;
					} else {
						global_obj.win_alert_auto_close(data.msg, '', 1000, '8%');
					}
				}, 'json');
			}, 'confirm');
		});
		if ($('input[name=domain_binding_verification]').length) {
			var $domain = $('input[name=domain_binding_verification]').val();
			setTimeout(function () {
				$.post('/manage/view/domain-binding/verification/', { 'DomainBinding': $domain }, function (data) {
					$('label.title').text(data.msg);
					if (data.ret != 1) {
						$('.input.tips').fadeIn();
					}
				}, 'json');
			}, 1000);
		}

		$('.domain_binding_verification_btn').click(function () {
			$('.verification_table tbody tr[data-status=0]').each(function () {
				var $this = $(this),
					$dm = $this.attr('data-domain'),
					$type = $this.attr('data-type');
				$.post('/manage/view/domain-binding/verification/', { 'DomainBinding': $dm, 'Type': $type }, function (data) {
					$this.find('.status').text(data.msg);
				}, 'json');
			});
			$(this).fadeOut();
		});

		frame_obj.submit_form_init($('#domain_binding_edit_form'), '/manage/view/domain-binding/', '', '', function (data) {
			global_obj.win_alert_auto_close(data.msg[0], data.ret == 1 ? '' : 'fail', 1000, '8%');
			setTimeout(function () {
				if (data.ret == 1) {
					location.href = '/manage/view/domain-binding/edit?status=2&domain=' + data.msg[1];
				} else {
					location.href = '/manage/view/domain-binding/';
				}
			}, 1000);
		});
	},
	/******************* 绑定域名 *******************/
	/******************* 网站地图设置 *******************/
	config_sitemap_edit: function () {
		//多语言事件
		frame_obj.submit_form_init($('#set_edit_form'), '/manage/view/seo/');

		frame_obj.multi_lang_show_all('#set_edit_form');
		frame_obj.multi_lang_show_item('#set_edit_form');
		//修改 SEO 关键词
		frame_obj.fixed_right($('#edit_keyword'), '.fixed_edit_keyword', function ($this) {
			var $Type = $('input[name=Type]').val();
			frame_obj.seo_edit_keyword({ 'do_action': '/manage/action/seo-keyword-select', 'Type': $Type });
		});
		frame_obj.seo_keyword_form_submit();

	}
}