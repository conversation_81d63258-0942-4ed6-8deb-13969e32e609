using Aop.Api.Domain;
using Entitys;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using NetTaste;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OfficeOpenXml.FormulaParsing.Excel.Functions.RefAndLookup;
using System.Linq.Expressions;
using System.Reflection;
using System.Text.Json;
using System.Text.RegularExpressions;
using YseStore.Common;
using YseStore.Common.Helper;
using YseStore.IService;
using YseStore.IService.Visual;
using YseStore.Model.RequestModels.Store;
using YseStore.Service;
using YseStore.Service.Visual;

namespace YseStoreAdmin.Controllers
{
    /// <summary>
    /// 店铺装修控制器
    /// </summary>
    [Route("api/[controller]")]
    //[ApiController]
    public class VislualController : Controller
    {

        public readonly IVisualDraftsService _visualDraftsService;
        public readonly IVisualPagesService _visualPagesService;
        public readonly IVisualPluginsService _visualPluginsService;
        public readonly IVisualTemplateServicee _visualTemplateServicee;
        public readonly IConfigService _configService;
        public readonly IPhotoService _photoServices;

        private readonly ILogger<CodeEditController> _logger;
        public VislualController(IVisualDraftsService visualDraftsService, IVisualPagesService visualPagesService, IVisualPluginsService visualPluginsService, IVisualTemplateServicee visualTemplateServicee, ILogger<CodeEditController> logger, IConfigService configService, IPhotoService photoServices)
        {
            _visualDraftsService = visualDraftsService;
            _visualPagesService = visualPagesService;
            _visualPluginsService = visualPluginsService;
            _visualTemplateServicee = visualTemplateServicee;
            _logger = logger;
            _configService = configService;
            _photoServices = photoServices;
        }

        //[HttpPost("/manage/view/visual-v2/save/")]
        //[ProducesResponseType(StatusCodes.Status200OK)]
        //[ProducesResponseType(StatusCodes.Status400BadRequest)]
        //[ProducesResponseType(StatusCodes.Status500InternalServerError)]
        //public async Task<IActionResult> Save([FromForm] IFormCollection obj)
        //{
        //    var s = obj.ToJson().JsonToObj<List<NameValuePair>>();

        //    var result = JsonHierarchyConverter.ConvertToHierarchicalJson(s);



        //    return Content("");
        //}


        /// <summary>
        /// 保存
        /// </summary>
        /// <returns></returns>
        [HttpPost("/manage/view/visual-v2/save/")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Save([FromForm] IFormCollection obj)
        {

            // 解析参数
            int editPId = Request.GetFormInt("EditPId");
            int draftsId = Request.GetFormInt("DraftsId");
            int pagesId = Request.GetFormInt("PagesId");
            string pageType = Request.GetFormString("Page");
            string AssociationId = Request.GetFormString("AssociationId");


            string PId = Request.GetFormString("PId[]");
            List<int> pIdList = PId.Split(',')
                .Select(it => int.Parse(it))
                .ToList();

            // 初始化参数

            var drafts = await _configService.GetConfigByGroup("global", "DraftsIdV2");
            var tmpdrafts = await _configService.GetConfigByGroup("global", "TmpDraftsIdV2");
            int currentDraftsId = drafts == null ? 0 : int.Parse(drafts.Value);
            int tmpDraftsIdV2 = tmpdrafts == null ? 0 : int.Parse(tmpdrafts.Value);
            long currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();


            // 验证必需参数
            if (draftsId == 0 || pagesId == 0 || pIdList == null || pIdList.Count == 0)
            {

                return Ok(new { ret = 0, msg = "参数错误" });
            }

            // 获取草稿数据
            var draft = await _visualDraftsService.QueryById(draftsId);
            if (draft == null)
                //return NotFound(new { code = 0, message = "draftsRow not found" });
                return Ok(new { ret = 0, msg = "draftsRow not found" });

            // 检查是否已发布的草稿
            if (draftsId == currentDraftsId)
                //return Conflict(new { code = 0, message = "不能修改已发布的草稿" });
                return Ok(new { ret = 0, msg = "不能修改已发布的草稿" });

            // 查询页面配置
            bool isSinglePage = new[] { "landing_page", "cod_page" }.Contains(pageType);

            int draid = isSinglePage && !AssociationId.IsNullOrEmpty() ? 0 : draftsId;
            var pageRow = await _visualPagesService.QueryVisualPagesAsync(pagesId, draid, pageType);
            if (pageRow == null)
                //return NotFound(new { code = 0, message = "pageRow not found" });
                return Ok(new { ret = 0, msg = "pageRow not found" });

            // 查询关联插件
            var plugins = (await _visualPluginsService.QueryByIDs(pIdList.ConvertAll(x => (object)x).ToArray()))
                .ToDictionary(p => p.PId);


            //visual[PId-24529][Blocks][Carousel-1][ButtonTextSizeMobile]
            //visual[PId-24529][Settings][CarouselInterval]
            //visual[PId-24528][Config][Display]

            var s = obj.ToJson().JsonToObj<List<YseStore.Common.Helper.KeyValuePair>>();

            var result = JsonHierarchyConverter.ConvertToHierarchicalJson(s);
            JObject VisualData = result["visual"].ToObject<JObject>();



            if (VisualData != null)
            {
                var updateData = new List<visual_plugins>();

                if (editPId > 0 && VisualData.ContainsKey($"PId-{editPId}"))
                {
                    var pluginData = VisualData[$"PId-{editPId}"].ToObject<JObject>();
                    if (plugins.TryGetValue(editPId, out var plugin))
                    {
                        updateData.Add(VisualDataCompared(plugin, pluginData));
                    }
                }
                else if (editPId == 0)
                {
                    foreach (var kvp in plugins)
                    {
                        if (VisualData.ContainsKey($"PId-{kvp.Key}"))
                        {
                            var pluginData = VisualData[$"PId-{kvp.Key}"].ToObject<JObject>();
                            updateData.Add(VisualDataCompared(kvp.Value, pluginData));
                        }
                    }
                }

                if (updateData.Count > 0)
                {
                    await _visualPluginsService.Update(updateData);
                }
            }


            // 处理全局配置
            if (VisualData != null && VisualData.ContainsKey("Config"))
            {
                draft.Config = VisualData["Config"].ToJson();
                draft.AccTime = (int)(currentTime);
                await _visualDraftsService.Update(draft);
            }

            // 更新页面插件顺序
            var updatedPlugins = isSinglePage && !AssociationId.IsNullOrEmpty()
                ? pIdList.Skip(1).SkipLast(1).ToList() // 移除首尾元素
                : pIdList;

            pageRow.Plugins = updatedPlugins.ToJson();
            await _visualPagesService.Update(pageRow);

            // 检查模板更新
            await CheckTemplate(pageRow.PagesId);

            // 清除临时草稿标识
            if (draftsId == tmpDraftsIdV2)
            {
                var r = await _configService.SetConfig("global", "TmpDraftsIdV2", "0");
            }

            // 记录操作日志
            //_operationLogService.Add("编辑店铺界面");

            return Ok(new { ret = 1, msg = "" });

        }


        /// <summary>
        /// 保存  (停用备份)
        /// </summary>
        /// <returns></returns>
        [HttpPost("/manage/view/visual-v2/save1/")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Save1([FromForm] SaveRequestModel request)
        {

            // 初始化参数

            var drafts = await _configService.GetConfigByGroup("global", "DraftsIdV2");
            var tmpdrafts = await _configService.GetConfigByGroup("global", "TmpDraftsIdV2");
            int currentDraftsId = drafts == null ? 0 : int.Parse(drafts.Value);
            int tmpDraftsIdV2 = tmpdrafts == null ? 0 : int.Parse(tmpdrafts.Value);
            long currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            // 解析参数
            int editPId = request.EditPId ?? 0;
            int draftsId = request.DraftsId ?? 0;
            int pagesId = request.PagesId ?? 0;
            string pageType = request.Page;
            // 验证必需参数
            if (draftsId == 0 || pagesId == 0 || request.PId == null || request.PId.Count == 0)
            {

                return Ok(new { ret = 0, msg = "参数错误" });
            }

            // 获取草稿数据
            var draft = await _visualDraftsService.QueryById(draftsId);
            if (draft == null)
                //return NotFound(new { code = 0, message = "draftsRow not found" });
                return Ok(new { ret = 0, msg = "draftsRow not found" });

            // 检查是否已发布的草稿
            if (draftsId == currentDraftsId)
                //return Conflict(new { code = 0, message = "不能修改已发布的草稿" });
                return Ok(new { ret = 0, msg = "不能修改已发布的草稿" });

            // 查询页面配置
            bool isSinglePage = new[] { "landing_page", "cod_page" }.Contains(pageType);

            int draid = isSinglePage && request.AssociationId.HasValue ? 0 : draftsId;
            var pageRow = await _visualPagesService.QueryVisualPagesAsync(pagesId, draid, pageType);
            if (pageRow == null)
                //return NotFound(new { code = 0, message = "pageRow not found" });
                return Ok(new { ret = 0, msg = "pageRow not found" });

            // 查询关联插件
            var plugins = (await _visualPluginsService.QueryByIDs(request.PId.ConvertAll(x => (object)x).ToArray()))
                .ToDictionary(p => p.PId);

            // 处理可视化数据更新
            if (request.VisualData != null)
            {
                var updateData = new List<visual_plugins>();

                if (editPId > 0 && request.VisualData.ContainsKey($"PId-{editPId}"))
                {
                    var pluginData = request.VisualData[$"PId-{editPId}"];
                    if (plugins.TryGetValue(editPId, out var plugin))
                    {

                        updateData.Add(VisualDataCompared(plugin, pluginData));
                    }
                }
                else if (editPId == 0)
                {
                    foreach (var kvp in plugins)
                    {
                        if (request.VisualData.ContainsKey($"PId-{kvp.Key}"))
                        {
                            var pluginData = request.VisualData[$"PId-{kvp.Key}"];
                            updateData.Add(VisualDataCompared(kvp.Value, pluginData));
                        }
                    }
                }

                if (updateData.Count > 0)
                {
                    await _visualPluginsService.Update(updateData);
                }
            }

            // 处理全局配置
            if (request.VisualData != null && request.VisualData.ContainsKey("Config"))
            {
                draft.Config = request.VisualData["Config"].ToJson();
                draft.AccTime = (int)(currentTime);
                await _visualDraftsService.Update(draft);
            }

            // 更新页面插件顺序
            var updatedPlugins = isSinglePage && request.AssociationId.HasValue
                ? request.PId.Skip(1).SkipLast(1).ToList() // 移除首尾元素
                : request.PId;

            pageRow.Plugins = updatedPlugins.ToJson();
            await _visualPagesService.Update(pageRow);

            // 检查模板更新
            await CheckTemplate(pageRow.PagesId);

            // 清除临时草稿标识
            if (draftsId == tmpDraftsIdV2)
            {
                var r = await _configService.SetConfig("global", "TmpDraftsIdV2", "0");
            }

            // 记录操作日志
            //_operationLogService.Add("编辑店铺界面");

            return Ok(new { ret = 1, msg = "" });

        }

        #region
        public visual_plugins VisualDataCompared(visual_plugins plugin, Dictionary<string, string> postData)
        {
            return new visual_plugins
            {
                Type = plugin.Type,
                Mode = plugin.Mode,
                PId = plugin.PId,
                Settings = postData.ContainsKey("Settings")
                    ? postData["Settings"].ToJson() : plugin.Settings,
                Blocks = postData.ContainsKey("Blocks")
                    ? postData["Blocks"].ToJson() : plugin.Blocks,
                Config = postData.ContainsKey("Config")
                    ? postData["Config"].ToJson() : plugin.Config
            };
        }

        public visual_plugins VisualDataCompared(visual_plugins plugin, JObject postData)
        {
            return new visual_plugins
            {
                Type = plugin.Type,
                Mode = plugin.Mode,
                PId = plugin.PId,
                Settings = postData.ContainsKey("Settings")
                    ? postData["Settings"].ToJson() : plugin.Settings,
                Blocks = postData.ContainsKey("Blocks")
                    ? postData["Blocks"].ToJson() : plugin.Blocks,
                Config = postData.ContainsKey("Config")
                    ? postData["Config"].ToJson() : plugin.Config
            };
        }

        public async Task CheckTemplate(int pagesId)
        {
            var page = await _visualPagesService.QueryById(pagesId);
            if (page != null && page.Plugins.Any())
            {
                var pluginIds = page.Plugins.JsonToObj<List<int>>();// JsonSerializer.Deserialize<List<int>>(page.Plugins);
                if (pluginIds == null || !pluginIds.Any())
                {
                    return;
                }
                // var plugins = await _visualPluginsService.Query(x => pluginIds.Contains(x.PId));
                var plugins = (await _visualPluginsService.QueryByIDs(pluginIds.ConvertAll(x => (object)x).ToArray()))
                .ToDictionary(p => p.PId);
                // 构建模板签名
                var signature = string.Join("|", pluginIds
                    .Select(id => $"{plugins[id].Type}-{plugins[id].Mode}"));

                // 检查模板是否存在
                var existingTemplate = await _visualTemplateServicee.QueryByClauseAsync(it => it.Pages == page.Pages && it.Content == signature && it.DraftsId == page.DraftsId);
                int newTemplateId = 0;
                if (existingTemplate != null && existingTemplate.TemplateId != page.TemplateId)
                {
                    newTemplateId = existingTemplate.TemplateId;
                }
                else if (existingTemplate == null)
                {
                    var newTemplate = new visual_template
                    {
                        DraftsId = page.DraftsId,
                        Pages = page.Pages,
                        Content = signature
                    };
                    newTemplateId = await _visualTemplateServicee.AddWithIntId(newTemplate);
                }

                // 清理旧模板 
                if (newTemplateId > 0)
                {
                    int oldTemplateId = page.TemplateId ?? 0;
                    if (page.TemplateId != newTemplateId)
                    {
                        await _visualPagesService.UpdateAsync(it => new visual_pages { TemplateId = newTemplateId }, it => it.PagesId == page.PagesId);
                    }
                    // 原模板没用上的删除
                    int isusing = await _visualPagesService.GetCountAsync(it => it.TemplateId == oldTemplateId && it.DraftsId == page.DraftsId);
                    if (isusing <= 0)
                    {
                        await _visualTemplateServicee.DeleteAsync(it => it.TemplateId == oldTemplateId && it.DraftsId == page.DraftsId);
                    }
                }

            }

        }

        #endregion

        #region 编辑模板草稿
        [HttpPost]
        [Route("/manage/view/visual/change-tmp-drafts")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ChangeTmpDrafts([FromForm] int draftsId = 0, [FromForm] int returnType = 0)
        {
            try
            {

                long currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                // 复制草稿
                int newDraftsId = await CopyDraft(draftsId, isRename: false, returnType: 1, isTmp: 0);

                // 删除旧临时草稿
                var tmpdrafts = await _configService.GetConfigByGroup("global", "TmpDraftsIdV2");
                int tmpDraftsIdV2 = tmpdrafts == null ? 0 : int.Parse(tmpdrafts.Value);
                if (newDraftsId > 0 && tmpDraftsIdV2 > 0)
                {
                    await DeleteDraftData(tmpDraftsIdV2);
                }

                // 更新全局配置
                await _configService.SetConfig("global", "TmpDraftsIdV2", newDraftsId.ToString());

                return Ok(new
                {
                    ret = 1,
                    msg = newDraftsId > 0 ? newDraftsId : draftsId
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    ret = 0,
                    msg = ex.Message
                });
            }
        }
        #endregion

        #region
        [HttpPost("Copy")]
        public async Task<IActionResult> Copy([FromForm] CopyDraftRequest request)
        {
            try
            {
                int newDraftsId = await CopyDraft(request.DraftsId, request.IsRename, request.ReturnType,
                    request.IsTmp);

                if (request.ReturnType == 1)
                    return Ok(new { code = 1, data = newDraftsId });

                return Ok(new { code = 1, message = "复制成功" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    code = 0,
                    message = ex.Message
                });
            }
        }

        private async Task<int> CopyDraft(int sourceDraftsId, bool isRename, int returnType, int isTmp)
        {
            if (sourceDraftsId <= 0) throw new ArgumentException("源草稿ID无效");
            try
            {
                long currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                // 1. 复制草稿主记录
                var sourceDraft = await _visualDraftsService.QueryByClauseAsync(d => d.DraftsId == sourceDraftsId);

                if (sourceDraft == null)
                    throw new Exception("源草稿不存在");

                var newDraft = new visual_drafts
                {
                    Name = sourceDraft.Name,
                    Themes = sourceDraft.Themes,
                    Config = sourceDraft.Config,
                    AccTime = (int)currentTime
                };

                int newDraftsId = await _visualDraftsService.AddWithIntId(newDraft);

                // 2. 处理模板表
                var pages = await _visualPagesService.Query(p => p.DraftsId == sourceDraftsId);

                if (!pages.Any())
                    throw new Exception("源草稿没有页面");

                var templateIds = pages.Select(p => p.TemplateId).Distinct().ToList();
                var templates = await _visualTemplateServicee.Query(t => templateIds.Contains(t.TemplateId));

                var templateMapping = new Dictionary<int, int>();

                foreach (var template in templates)
                {
                    var newTemplate = new visual_template
                    {
                        DraftsId = newDraftsId,
                        Pages = template.Pages,
                        Content = template.Content
                    };

                    int newTemplateId = await _visualTemplateServicee.AddWithIntId(newTemplate);
                    templateMapping.Add(template.TemplateId, newTemplateId);
                }

                // 3. 复制页面和插件
                int headerId = 0, footerId = 0;
                var pageHeaderFooterMapping = new Dictionary<int, (int HeaderId, int FooterId)>();

                for (int i = 0; i < pages.Count; i++)
                {
                    var page = pages[i];
                    bool isFirstPage = i == 0;

                    // 获取页面插件ID列表
                    var pluginIds =
                        System.Text.Json.JsonSerializer.Deserialize<List<int>>(page.Plugins);

                    // 查询原始插件数据
                    var plugins = (await _visualPluginsService.Query(p => pluginIds.Contains(p.PId))).ToDictionary(p => p.PId);

                    // 复制插件
                    var newPluginIds = new List<int>();

                    foreach (int pid in pluginIds)
                    {
                        if (plugins.TryGetValue(pid, out var plugin))
                        {
                            // 非首页跳过页眉页脚
                            if (!isFirstPage && (plugin.Type == "header" || plugin.Type == "footer"))
                                continue;

                            // 复制插件
                            var newPlugin = new visual_plugins
                            {
                                //DraftsId = newDraftsId,
                                Type = plugin.Type,
                                Mode = plugin.Mode,
                                Settings = plugin.Settings,
                                Blocks = plugin.Blocks,
                                Config = plugin.Config
                            };
                            int newPId = await _visualPluginsService.AddWithIntId(newPlugin);
                            newPluginIds.Add(newPId);
                            // 记录页眉页脚
                            if (isFirstPage && plugin.Type == "header")
                                headerId = newPId;
                            if (isFirstPage && plugin.Type == "footer")
                                footerId = newPId;
                        }
                    }

                    // 非首页添加页眉页脚
                    if (!isFirstPage && headerId != 0 && footerId != 0)
                    {
                        newPluginIds.Insert(0, headerId);
                        newPluginIds.Add(footerId);
                    }

                    // 记录当前页使用的页眉页脚
                    if (isFirstPage && headerId != 0 && footerId != 0)
                    {
                        pageHeaderFooterMapping.Add(newDraftsId, (headerId, footerId));
                    }
                    else if (pageHeaderFooterMapping.ContainsKey(newDraftsId))
                    {
                        var (hId, fId) = pageHeaderFooterMapping[newDraftsId];
                        newPluginIds.Insert(0, hId);
                        newPluginIds.Add(fId);
                    }

                    // 复制页面
                    var newPage = new visual_pages
                    {
                        DraftsId = newDraftsId,
                        Pages = page.Pages,
                        AssociationId = page.AssociationId,
                        Plugins = newPluginIds.ToJson(),
                        TemplateId = templateMapping.TryGetValue(page.TemplateId ?? 0, out int newTemplateId) ?
                            newTemplateId : 0
                    };
                    await _visualPagesService.AddWithIntId(newPage);
                }
                return newDraftsId;
            }
            catch
            {
                return 0;
            }


        }

        private async Task DeleteDraftData(int draftsId)
        {

            // 获取关联页面和插件ID
            var pages = await _visualPagesService.Query(p => p.DraftsId == draftsId);

            var pluginIds = pages.SelectMany(p => System.Text.Json.JsonSerializer.Deserialize<List<int>>(p.Plugins)).Distinct().ToList();

            // 删除插件数据
            if (pluginIds.Any())
            {
                await _visualPluginsService.DeleteAsync(p => pluginIds.Contains(p.PId));
            }

            // 删除页面数据
            await _visualPagesService.DeleteAsync(p => p.DraftsId == draftsId);

            // 删除模板数据
            await _visualTemplateServicee.DeleteAsync(t => t.DraftsId == draftsId);

            // 删除草稿主记录
            await _visualDraftsService.DeleteAsync(d => d.DraftsId == draftsId);
        }

        #endregion

        /// <summary>
        /// 计算等比缩小后的高度
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("/manage/view/visual-v2/calculate-image-ratio")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CalculateImageRatio()
        {

            try
            {
                int PluginsId = Request.GetFormInt("pluginsId");
                string Value = Request.GetFormString("value");
                string Name = Request.GetFormString("Name").Replace("visual", "");


                // Validate input
                if (PluginsId == 0 || string.IsNullOrEmpty(Value) || string.IsNullOrEmpty(Name))
                {
                    return Ok(new { ret = -1, msg = "Invalid parameters" });
                }

                string name = Name.Trim().TrimStart("visual".ToCharArray());

                // Get visual plugin data
                var visualRow = await _visualPluginsService.QueryByClauseAsync(it => it.PId == PluginsId);

                if (visualRow == null)
                {
                    return Ok(new { ret = -2, msg = "Visual plugin not found" });
                }

                string configPath = System.IO.Path.Combine(System.Environment.CurrentDirectory, $"wwwroot/template/default/cusvis_mode/{visualRow.Type}/{visualRow.Mode}/config.json");



                if (!System.IO.File.Exists(configPath))
                {
                    return Ok(new { ret = -3, msg = "Config file not found" });
                }

                string configContent = System.IO.File.ReadAllText(configPath);
                var config = JsonConvert.DeserializeObject<JObject>(configContent);

                // Parse name using regex
                var matches = Regex.Matches(name, @"(?<=\[)[^]]+");
                string jsName = "input[name=\"visual";
                JToken picHeightObj = null;
                JToken picObj = null;
                string dataMod = "PicHeight";

                if (matches.Count == 3)
                {
                    string dataPId = matches[0].Value;
                    string dataSet = matches[1].Value; // Settings
                    string dataType = matches[2].Value; // Pic

                    picHeightObj = config[dataSet]?[dataMod];
                    picObj = config[dataSet]?[dataType];

                    if (picHeightObj == null)
                    {
                        dataMod = "PosterPicHeight";
                        picHeightObj = config[dataSet]?["PosterPicHeight"];
                    }

                    jsName += "[" + dataPId + "][" + dataSet + "][" + dataMod + "]";
                }
                else if (matches.Count == 4)
                {
                    string dataPId = matches[0].Value;
                    string dataPos = matches[1].Value;
                    string dataSet = matches[2].Value; // Blocks
                    string dataType = matches[3].Value;

                    picHeightObj = config[dataPos]?[dataSet]?["PicHeight"];
                    picObj = config[dataPos]?[dataSet]?[dataType];

                    if (picHeightObj == null)
                    {
                        dataMod = "PosterPicHeight";
                        picHeightObj = config[dataPos]?[dataSet]?["PosterPicHeight"];
                    }

                    jsName += "[" + dataPId + "][" + dataPos + "][" + dataSet + "][" + dataMod + "]";
                }
                else
                {
                    return Ok(new { ret = -3, msg = "Invalid name format" });
                }

                jsName += "\"]";

                // Validate picHeightObj
                if (picHeightObj == null || picHeightObj["type"]?.ToString() != "progress")
                {
                    return Ok(new
                    {
                        ret = -3,
                        msg = "Invalid height object",
                        data = new { picHeightObj, matches = matches.Select(m => m.Value).ToList() }
                    });
                }

                // Get recommended dimensions
                int width = picObj["expand"]?["width"]?.Value<int>() ?? 0;
                int height = picObj["expand"]?["height"]?.Value<int>() ?? 0;

                if (width <= 0 || height <= 0)
                {
                    return Ok(new { ret = -3, msg = "Invalid dimensions in config" });
                }

                // Get height options
                var options = picHeightObj["options"]?.ToObject<List<int>>();
                if (options == null || options.Count == 0)
                {
                    return Ok(new { ret = -3, msg = "Invalid height options" });
                }

                int max = options.Max();
                int min = options.Min();

                // Get uploaded photo dimensions
                var photoRow = await _photoServices.QueryByClauseAsync(it => it.PicPath == Value);

                if (photoRow == null)
                {
                    return Ok(new { ret = -4, msg = "Photo not found" });
                }

                int getWidth = photoRow.Width;
                int getHeight = photoRow.Height;

                if (getWidth <= 0 || getHeight <= 0)
                {
                    return Ok(new { ret = -4, msg = "Invalid photo dimensions" });
                }

                // Calculate scaled height
                double getScale = getHeight * width / (double)getWidth;
                int resultHeight = (int)Math.Round(getScale);

                // Check if within range
                if (resultHeight >= min && resultHeight <= max)
                {
                    return Ok(new
                    {
                        ret = 1,
                        msg = new
                        {
                            PicHeight = resultHeight,
                            InputObj = jsName
                        }
                    });
                }
                else
                {
                    return Ok(new
                    {
                        ret = -5,
                        msg = new { PicHeight = resultHeight }
                    });
                }
            }
            catch (Exception ex)
            {
                return Ok(new { ret = -99, msg = ex.Message });
            }

        }


    
        /// <summary>
        /// 添加模块
        /// </summary>
        /// <returns></returns>
        //[HttpPost]
        //[Route("/manage/view/visual-v2/plugins-add/")]
        //[ProducesResponseType(StatusCodes.Status200OK)]
        //[ProducesResponseType(StatusCodes.Status400BadRequest)]
        //[ProducesResponseType(StatusCodes.Status500InternalServerError)]
        //public async Task<IActionResult> PluginsAdd()
        //{

        //    try
        //    {
        //        int DraftsId = Request.GetFormInt("DraftsId");
        //        int PagesId = Request.GetFormInt("PagesId");
        //        string Page = Request.GetFormString("Page", "index");
        //        int Id = Request.GetFormInt("Id");
        //        string Type = Request.GetFormString("Type");
        //        string Mode = Request.GetFormString("Mode");

        //        // Validate required parameters
        //        if (string.IsNullOrEmpty(Type))
        //        {
        //            return Ok(new { ret = -1, msg = "Type is required" });
        //        }

        //        // Get draft data
        //        var draftsRow = await _visualDraftsService.QueryByClauseAsync(it => it.DraftsId == DraftsId);


        //        if (draftsRow == null)
        //        {
        //            return Ok(new { success = false, message = "Draft not found" });
        //        }

        //        // Call service method
        //        //var service = new VisualServiceV2();
        //        //var result = service.PluginsAdd(
        //        //    draftsId: request.DraftsId,
        //        //    pagesId: request.PagesId ?? 0,
        //        //    page: request.Page ?? "index",
        //        //    type: request.Type,
        //        //    mode: request.Mode,
        //        //    id: request.Id ?? 0
        //        //);

        //        //// Check template
        //        //HelpsVisualV2.CheckTemplate(PagesId ?? 0);

        //        if (result.Ret == 1)
        //        {
        //            // Log operation
        //            _logger.LogInformation("增加店铺界面模块");
        //            //HelpsManage.OperationLog("增加店铺界面模块");

        //            return Ok(new { success = true, pId = PId });
        //        }
        //        else
        //        {
        //            return BadRequest(new { success = false, message = "Operation failed" });
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, "Error in PluginsAdd");
        //        return StatusCode(500, new { success = false, message = "Internal server error" });
        //    }

        //}



    }


}
